#!/usr/bin/env python3
"""
检查主池中"error"服务商的问题
"""

import asyncio
from app.database.connection import database
from app.database.models import Proxy
from sqlalchemy import select, func

async def check_error_provider():
    """检查error服务商的问题"""
    await database.connect()
    
    try:
        print("=== 检查主池中的服务商分配 ===\n")
        
        # 1. 统计各个池的服务商分布
        print("📊 各池服务商统计:")
        query = select(Proxy.pool, Proxy.provider, func.count(Proxy.id).label('count')).group_by(Proxy.pool, Proxy.provider)
        results = await database.fetch_all(query)
        
        pool_stats = {}
        for row in results:
            pool = row['pool'] or 'unknown'
            provider = row['provider'] or 'unknown'
            count = row['count']
            
            if pool not in pool_stats:
                pool_stats[pool] = {}
            pool_stats[pool][provider] = count
        
        for pool, providers in pool_stats.items():
            print(f"\n{pool}池:")
            for provider, count in providers.items():
                print(f"  {provider}: {count:,} 个代理")
        
        # 2. 专门检查主池中的"error"服务商
        print(f"\n🔍 主池中的'error'服务商详情:")
        error_query = select(Proxy).where(
            (Proxy.pool == 'main') & 
            (Proxy.provider == 'error')
        ).limit(10)
        
        error_proxies = await database.fetch_all(error_query)
        
        if error_proxies:
            print(f"发现 {len(error_proxies)} 个'error'服务商的代理（显示前10个）:")
            for i, proxy in enumerate(error_proxies):
                print(f"  {i+1}. {proxy.proxy_url}")
                print(f"     API组: {proxy.api_group}")
                print(f"     地区: {proxy.region}")
                print(f"     匿名性: {proxy.anonymity}")
                print(f"     延迟: {proxy.last_latency_ms}ms")
                print()
        else:
            print("没有发现'error'服务商的代理")
        
        # 3. 检查所有主池代理的服务商分配
        print(f"\n📋 主池代理服务商分配详情:")
        main_query = select(Proxy.provider, Proxy.api_group, func.count(Proxy.id).label('count')).where(
            Proxy.pool == 'main'
        ).group_by(Proxy.provider, Proxy.api_group)
        
        main_results = await database.fetch_all(main_query)
        
        if main_results:
            print("主池分配:")
            for row in main_results:
                provider = row['provider'] or 'unknown'
                api_group = row['api_group'] or 'unknown'
                count = row['count']
                print(f"  {provider}/{api_group}: {count:,} 个代理")
        else:
            print("主池为空")
        
        # 4. 检查服务商配置
        print(f"\n⚙️  检查服务商配置:")
        try:
            from extensions.provider.config import get_provider_configs
            provider_configs = await get_provider_configs()
            
            print("配置的服务商:")
            for name, config in provider_configs.items():
                print(f"  {name}: enabled={config.enabled}, api_groups={config.api_groups}")
        
        except Exception as e:
            print(f"获取服务商配置失败: {e}")
        
        # 5. 检查是否有代理没有正确分配服务商
        print(f"\n🔍 检查未正确分配的代理:")
        unassigned_query = select(Proxy).where(
            (Proxy.pool == 'main') & 
            ((Proxy.provider == None) | (Proxy.provider == '') | (Proxy.provider == 'error'))
        ).limit(5)
        
        unassigned_proxies = await database.fetch_all(unassigned_query)
        
        if unassigned_proxies:
            print(f"发现 {len(unassigned_proxies)} 个未正确分配的主池代理:")
            for proxy in unassigned_proxies:
                print(f"  {proxy.proxy_url} -> provider: '{proxy.provider}', api_group: '{proxy.api_group}'")
        else:
            print("所有主池代理都已正确分配")
            
    finally:
        await database.disconnect()

async def main():
    """主函数"""
    await check_error_provider()

if __name__ == "__main__":
    asyncio.run(main())
