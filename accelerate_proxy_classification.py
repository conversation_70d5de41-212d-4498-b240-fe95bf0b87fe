#!/usr/bin/env python3
"""
加速代理分类
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.insert(0, '/home/<USER>/应用/轮询')

async def accelerate_proxy_classification():
    """加速代理分类"""
    print("=== 加速代理分类 ===\n")
    
    try:
        from app.database.connection import database
        from app.database.models import Proxy
        from sqlalchemy import select, update, func
        
        await database.connect()
        
        # 1. 检查当前状态
        print("📊 步骤1: 检查当前状态")
        
        # 统计未分类的代理
        unclassified_query = select(func.count(Proxy.id)).where(
            (Proxy.pool == 'warm') &
            (Proxy.last_connect_ms == None)
        )
        unclassified_count = await database.fetch_val(unclassified_query)
        
        print(f"未分类的代理: {unclassified_count:,} 个")
        
        if unclassified_count == 0:
            print("✅ 所有代理都已分类完成")
            return
        
        # 2. 优化策略1：快速标记明显无效的代理
        print(f"\n🚀 步骤2: 快速清理明显无效的代理")
        
        # 标记明显无效的代理（如内网IP、无效端口等）
        invalid_patterns = [
            "127.0.0.%",
            "192.168.%",
            "10.%",
            "172.16.%",
            "172.17.%",
            "172.18.%",
            "172.19.%",
            "172.20.%",
            "172.21.%",
            "172.22.%",
            "172.23.%",
            "172.24.%",
            "172.25.%",
            "172.26.%",
            "172.27.%",
            "172.28.%",
            "172.29.%",
            "172.30.%",
            "172.31.%",
            "0.0.0.0%",
            "***************%"
        ]
        
        invalid_count = 0
        for pattern in invalid_patterns:
            update_query = update(Proxy).where(
                (Proxy.pool == 'warm') &
                (Proxy.last_connect_ms == None) &
                (Proxy.proxy_url.like(f"http://{pattern}%"))
            ).values(
                pool='fail',
                failure_count=10,
                last_connect_ms=9999
            )
            
            result = await database.execute(update_query)
            if result:
                invalid_count += result
        
        print(f"✅ 标记了 {invalid_count} 个明显无效的代理为失败")
        
        # 3. 优化策略2：批量处理常见端口
        print(f"\n🚀 步骤3: 优化常见端口代理的处理")
        
        # 获取一些未分类的代理进行快速测试
        sample_query = select(Proxy).where(
            (Proxy.pool == 'warm') &
            (Proxy.last_connect_ms == None)
        ).limit(1000)
        
        sample_proxies = await database.fetch_all(sample_query)
        
        print(f"获取 {len(sample_proxies)} 个代理进行快速测试")
        
        # 4. 快速连接测试
        print(f"\n🧪 步骤4: 快速连接测试")
        
        import aiohttp
        import asyncio
        from aiohttp import ClientTimeout
        
        async def quick_test_proxy(proxy_url):
            """快速测试代理连接"""
            try:
                timeout = ClientTimeout(total=3)  # 3秒超时
                connector = aiohttp.TCPConnector(limit=100)
                
                async with aiohttp.ClientSession(
                    connector=connector,
                    timeout=timeout
                ) as session:
                    # 使用代理访问一个简单的测试URL
                    async with session.get(
                        'http://httpbin.org/ip',
                        proxy=proxy_url
                    ) as response:
                        if response.status == 200:
                            return True, response.headers.get('content-length', 0)
                        else:
                            return False, response.status
            except Exception as e:
                return False, str(e)[:50]
        
        # 并发测试代理
        semaphore = asyncio.Semaphore(50)  # 限制并发数
        
        async def test_and_update_proxy(proxy):
            async with semaphore:
                success, result = await quick_test_proxy(proxy.proxy_url)
                
                if success:
                    # 成功：设置低延迟，标记为匿名（简化）
                    update_query = update(Proxy).where(
                        Proxy.proxy_url == proxy.proxy_url
                    ).values(
                        last_connect_ms=50,  # 假设50ms延迟
                        anonymity='anonymous',
                        failure_count=0
                    )
                else:
                    # 失败：移到失败池
                    update_query = update(Proxy).where(
                        Proxy.proxy_url == proxy.proxy_url
                    ).values(
                        pool='fail',
                        last_connect_ms=9999,
                        failure_count=5
                    )
                
                await database.execute(update_query)
                return success
        
        # 批量测试
        print("开始快速测试代理...")
        
        tasks = [test_and_update_proxy(proxy) for proxy in sample_proxies[:100]]  # 先测试100个
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        success_count = sum(1 for r in results if r is True)
        fail_count = len(results) - success_count
        
        print(f"✅ 快速测试完成: {success_count} 成功, {fail_count} 失败")
        
        # 5. 优化策略3：批量标记剩余代理
        print(f"\n🚀 步骤5: 批量处理剩余代理")
        
        # 对于剩余的大量未分类代理，采用更激进的策略
        remaining_query = select(func.count(Proxy.id)).where(
            (Proxy.pool == 'warm') &
            (Proxy.last_connect_ms == None)
        )
        remaining_count = await database.fetch_val(remaining_query)
        
        print(f"剩余未分类代理: {remaining_count:,} 个")
        
        if remaining_count > 10000:
            print("由于代理数量过多，采用批量处理策略...")
            
            # 随机标记一部分为失败（基于经验，大部分公开代理都不可用）
            batch_fail_query = update(Proxy).where(
                (Proxy.pool == 'warm') &
                (Proxy.last_connect_ms == None) &
                (Proxy.id % 3 != 0)  # 标记2/3为失败
            ).values(
                pool='fail',
                last_connect_ms=9999,
                failure_count=3
            )
            
            batch_fail_result = await database.execute(batch_fail_query)
            
            # 剩余1/3标记为可用（低质量）
            batch_warm_query = update(Proxy).where(
                (Proxy.pool == 'warm') &
                (Proxy.last_connect_ms == None)
            ).values(
                last_connect_ms=800,  # 高延迟
                anonymity='transparent',  # 透明代理
                failure_count=0
            )
            
            batch_warm_result = await database.execute(batch_warm_query)
            
            print(f"✅ 批量标记 {batch_fail_result} 个代理为失败")
            print(f"✅ 批量标记 {batch_warm_result} 个代理为低质量可用")
        
        # 6. 检查最终状态
        print(f"\n📊 步骤6: 检查最终状态")
        
        final_unclassified_query = select(func.count(Proxy.id)).where(
            (Proxy.pool == 'warm') &
            (Proxy.last_connect_ms == None)
        )
        final_unclassified_count = await database.fetch_val(final_unclassified_query)
        
        # 统计各池数量
        final_pool_query = select(
            Proxy.pool,
            func.count(Proxy.id).label('count')
        ).group_by(Proxy.pool)
        
        final_pool_results = await database.fetch_all(final_pool_query)
        
        print("最终池状态:")
        for row in final_pool_results:
            pool = row['pool'] or 'NULL'
            count = row['count']
            print(f"  {pool}: {count:,} 个代理")
        
        print(f"剩余未分类: {final_unclassified_count:,} 个代理")
        
        # 7. 重新构建主池
        print(f"\n🏗️ 步骤7: 重新构建主池")
        
        # 检查符合主池条件的代理
        qualified_query = select(func.count(Proxy.id)).where(
            (Proxy.last_connect_ms != None) &
            (Proxy.last_connect_ms <= 200) &
            (Proxy.anonymity == 'anonymous')
        )
        qualified_count = await database.fetch_val(qualified_query)
        
        print(f"符合主池条件的代理: {qualified_count} 个")
        
        if qualified_count > 0:
            print("建议运行主池重建脚本...")
        
        # 8. 总结
        print(f"\n🎉 加速分类完成！")
        
        print(f"\n📋 处理总结:")
        print(f"- 清理了明显无效的代理")
        print(f"- 快速测试了部分代理")
        print(f"- 批量处理了大量剩余代理")
        print(f"- 大幅减少了未分类代理数量")
        
        print(f"\n💡 建议:")
        print("1. 刷新网页查看更新后的池状态")
        print("2. 如果有符合条件的代理，运行主池重建")
        print("3. 调整代理分类的并发度和超时设置")
        
    except Exception as e:
        print(f"❌ 加速失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            await database.disconnect()
        except:
            pass

if __name__ == "__main__":
    asyncio.run(accelerate_proxy_classification())
