20:29:24.126639 [0-x] == Info: [READ] client_reset, clear readers
20:29:24.126760 [0-x] == Info: Uses proxy env variable http_proxy == 'http://127.0.0.1:7890'
20:29:24.126842 [0-0] == Info: [SETUP] added
  % Total    % Received % Xferd  Average Speed   Time    Time     Time  Current
                                 Dload  Upload   Total   Spent    Left  Speed

  0     0    0     0    0     0      0      0 --:--:-- --:--:-- --:--:--     020:29:24.127099 [0-0] == Info:   Trying 127.0.0.1:7890...
20:29:24.127240 [0-0] == Info: Connected to 127.0.0.1 (127.0.0.1) port 7890
20:29:24.127331 [0-0] == Info: using HTTP/1.x
20:29:24.127417 [0-0] => Send header, 124 bytes (0x7c)
0000: GET http://spys.me/proxy.txt HTTP/1.1
0027: Host: spys.me
0036: User-Agent: curl/8.12.1
004f: Accept: */*
005c: Proxy-Connection: Keep-Alive
007a: 
20:29:24.127697 [0-0] == Info: Request completely sent off

  0     0    0     0    0     0      0      0 --:--:--  0:00:01 --:--:--     020:29:25.832793 [0-0] <= Recv header, 32 bytes (0x20)
0000: HTTP/1.1 301 Moved Permanently
20:29:25.832958 [0-0] == Info: [WRITE] cw_out, wrote 32 header bytes -> 32
20:29:25.833021 [0-0] == Info: [WRITE] download_write header(type=c, blen=32) -> 0
20:29:25.833084 [0-0] == Info: [WRITE] client_write(type=c, len=32) -> 0
20:29:25.833140 [0-0] == Info: [WRITE] xfer_write_resp(len=32, eos=0) -> 0
20:29:25.833210 [0-0] == Info: [WRITE] looking for transfer decoder: chunked
20:29:25.833271 [0-0] == Info: [WRITE] added transfer decoder chunked -> 0
20:29:25.833327 [0-0] <= Recv header, 28 bytes (0x1c)
0000: Transfer-Encoding: chunked
20:29:25.833404 [0-0] == Info: [WRITE] header_collect pushed(type=1, len=28) -> 0
20:29:25.833467 [0-0] == Info: [WRITE] cw_out, wrote 28 header bytes -> 28
20:29:25.833523 [0-0] == Info: [WRITE] download_write header(type=4, blen=28) -> 0
20:29:25.833585 [0-0] == Info: [WRITE] client_write(type=4, len=28) -> 0
20:29:25.833641 [0-0] <= Recv header, 30 bytes (0x1e)
0000: Alt-Svc: h3=":443"; ma=86400
20:29:25.833710 [0-0] == Info: [WRITE] header_collect pushed(type=1, len=30) -> 0
20:29:25.833772 [0-0] == Info: [WRITE] cw_out, wrote 30 header bytes -> 30
20:29:25.833829 [0-0] == Info: [WRITE] download_write header(type=4, blen=30) -> 0
20:29:25.833890 [0-0] == Info: [WRITE] client_write(type=4, len=30) -> 0
20:29:25.833944 [0-0] <= Recv header, 30 bytes (0x1e)
0000: Cf-Ray: 9719a8098fe540d8-SIN
20:29:25.834013 [0-0] == Info: [WRITE] header_collect pushed(type=1, len=30) -> 0
20:29:25.834074 [0-0] == Info: [WRITE] cw_out, wrote 30 header bytes -> 30
20:29:25.834131 [0-0] == Info: [WRITE] download_write header(type=4, blen=30) -> 0
20:29:25.834193 [0-0] == Info: [WRITE] client_write(type=4, len=30) -> 0
20:29:25.834248 [0-0] <= Recv header, 24 bytes (0x18)
0000: Connection: keep-alive
20:29:25.834312 [0-0] == Info: [WRITE] header_collect pushed(type=1, len=24) -> 0
20:29:25.834373 [0-0] == Info: [WRITE] cw_out, wrote 24 header bytes -> 24
20:29:25.834438 [0-0] == Info: [WRITE] download_write header(type=4, blen=24) -> 0
20:29:25.834499 [0-0] == Info: [WRITE] client_write(type=4, len=24) -> 0
20:29:25.834554 [0-0] <= Recv header, 37 bytes (0x25)
0000: Date: Tue, 19 Aug 2025 12:29:25 GMT
20:29:25.834627 [0-0] == Info: [WRITE] header_collect pushed(type=1, len=37) -> 0
20:29:25.834685 [0-0] == Info: [WRITE] cw_out, wrote 37 header bytes -> 37
20:29:25.834741 [0-0] == Info: [WRITE] download_write header(type=4, blen=37) -> 0
20:29:25.834803 [0-0] == Info: [WRITE] client_write(type=4, len=37) -> 0
20:29:25.834858 [0-0] <= Recv header, 23 bytes (0x17)
0000: Keep-Alive: timeout=4
20:29:25.834921 [0-0] == Info: [WRITE] header_collect pushed(type=1, len=23) -> 0
20:29:25.834984 [0-0] == Info: [WRITE] cw_out, wrote 23 header bytes -> 23
20:29:25.835040 [0-0] == Info: [WRITE] download_write header(type=4, blen=23) -> 0
20:29:25.835103 [0-0] == Info: [WRITE] client_write(type=4, len=23) -> 0
20:29:25.835170 [0-0] <= Recv header, 37 bytes (0x25)
0000: Location: https://spys.me/proxy.txt
20:29:25.835246 [0-0] == Info: [WRITE] header_collect pushed(type=1, len=37) -> 0
20:29:25.835333 [0-0] == Info: [WRITE] cw_out, wrote 37 header bytes -> 37
20:29:25.835442 [0-0] == Info: [WRITE] download_write header(type=4, blen=37) -> 0
20:29:25.835552 [0-0] == Info: [WRITE] client_write(type=4, len=37) -> 0
20:29:25.835648 [0-0] == Info: [WRITE] xfer_write_resp(len=209, eos=0) -> 0
20:29:25.835760 [0-0] <= Recv header, 69 bytes (0x45)
0000: Nel: {"report_to":"cf-nel","success_fraction":0.0,"max_age":6048
0040: 00}
20:29:25.835945 [0-0] == Info: [WRITE] header_collect pushed(type=1, len=69) -> 0
20:29:25.836053 [0-0] == Info: [WRITE] cw_out, wrote 69 header bytes -> 69
20:29:25.836150 [0-0] == Info: [WRITE] download_write header(type=4, blen=69) -> 0
20:29:25.836259 [0-0] == Info: [WRITE] client_write(type=4, len=69) -> 0
20:29:25.836354 [0-0] <= Recv header, 30 bytes (0x1e)
0000: Proxy-Connection: keep-alive
20:29:25.836453 [0-0] == Info: [WRITE] header_collect pushed(type=1, len=30) -> 0
20:29:25.836542 [0-0] == Info: [WRITE] cw_out, wrote 30 header bytes -> 30
20:29:25.836598 [0-0] == Info: [WRITE] download_write header(type=4, blen=30) -> 0
20:29:25.836666 [0-0] == Info: [WRITE] client_write(type=4, len=30) -> 0
20:29:25.836735 [0-0] <= Recv header, 222 bytes (0xde)
0000: Report-To: {"group":"cf-nel","max_age":604800,"endpoints":[{"url
0040: ":"https://a.nel.cloudflare.com/report/v4?s=3XbWohp5lw0GLFBNwYwt
0080: 9Zd8BTrq%2BUSqSk7MurBfikI7BHJ6gVQBBqwPuZkaqu4%2BPxONyMYdCfGzod%2
00c0: F6LpVr%2FWRD%2FDRytMk%3D"}]}
20:29:25.837002 [0-0] == Info: [WRITE] header_collect pushed(type=1, len=222) -> 0
20:29:25.837064 [0-0] == Info: [WRITE] cw_out, wrote 222 header bytes -> 222
20:29:25.837121 [0-0] == Info: [WRITE] download_write header(type=4, blen=222) -> 0
20:29:25.837183 [0-0] == Info: [WRITE] client_write(type=4, len=222) -> 0
20:29:25.837237 [0-0] <= Recv header, 20 bytes (0x14)
0000: Server: cloudflare
20:29:25.837297 [0-0] == Info: [WRITE] header_collect pushed(type=1, len=20) -> 0
20:29:25.837356 [0-0] == Info: [WRITE] cw_out, wrote 20 header bytes -> 20
20:29:25.837424 [0-0] == Info: [WRITE] download_write header(type=4, blen=20) -> 0
20:29:25.837484 [0-0] == Info: [WRITE] client_write(type=4, len=20) -> 0
20:29:25.837546 [0-0] <= Recv header, 2 bytes (0x2)
0000: 
20:29:25.837610 [0-0] == Info: [WRITE] header_collect pushed(type=1, len=2) -> 0
20:29:25.837691 [0-0] == Info: [WRITE] cw_out, wrote 2 header bytes -> 2
20:29:25.837746 [0-0] == Info: [WRITE] download_write header(type=4, blen=2) -> 0
20:29:25.837807 [0-0] == Info: [WRITE] client_write(type=4, len=2) -> 0
20:29:25.837861 [0-0] <= Recv data, 5 bytes (0x5)
0000: 0
0003: 
20:29:25.837911 [0-0] == Info: [WRITE] http_chunk, response complete
20:29:25.837963 [0-0] == Info: [WRITE] client_write(type=1, len=5) -> 0
20:29:25.838025 [0-0] == Info: [WRITE] xfer_write_resp(len=348, eos=0) -> 0

  0     0    0     0    0     0      0      0 --:--:--  0:00:01 --:--:--     020:29:25.838165 [0-0] == Info: [WRITE] cw-out is notpaused

  0     0    0     0    0     0      0      0 --:--:--  0:00:01 --:--:--     0
20:29:25.838310 [0-0] == Info: [WRITE] cw-out done
20:29:25.838386 [0-0] == Info: [READ] client_reset, clear readers
20:29:25.838474 [0-0] == Info: Connection #0 to host 127.0.0.1 left intact
