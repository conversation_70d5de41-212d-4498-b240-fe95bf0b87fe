#!/usr/bin/env python3
"""
检查最终状态
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.insert(0, '/home/<USER>/应用/轮询')

async def check_final_status():
    """检查最终状态"""
    print("=== 检查最终状态 ===\n")
    
    try:
        from app.database.connection import database
        from app.database.models import Proxy
        from sqlalchemy import select, func
        
        await database.connect()
        
        # 1. 检查主池分配
        print("📊 步骤1: 检查主池分配")
        
        main_query = select(
            Proxy.provider,
            Proxy.api_group,
            func.count(Proxy.id).label('count')
        ).where(Proxy.pool == 'main').group_by(Proxy.provider, Proxy.api_group)
        
        main_results = await database.fetch_all(main_query)
        
        if main_results:
            print("数据库中的主池分配:")
            provider_totals = {}
            for row in main_results:
                provider = row['provider'] or 'NULL'
                api_group = row['api_group'] or 'NULL'
                count = row['count']
                
                if provider not in provider_totals:
                    provider_totals[provider] = 0
                provider_totals[provider] += count
                
                print(f"  {provider}/{api_group}: {count} 个代理")
            
            print(f"\n各服务商总计:")
            for provider, total in provider_totals.items():
                print(f"  {provider}: {total} 个代理")
        else:
            print("❌ 数据库中没有主池代理")
        
        # 2. 检查具体代理
        print(f"\n🔍 步骤2: 检查具体代理")
        
        detail_query = select(Proxy).where(Proxy.pool == 'main').order_by(Proxy.provider, Proxy.proxy_url)
        detail_results = await database.fetch_all(detail_query)
        
        if detail_results:
            print("主池代理详情:")
            current_provider = None
            for proxy in detail_results:
                if proxy.provider != current_provider:
                    current_provider = proxy.provider
                    print(f"\n{current_provider}:")
                
                print(f"  {proxy.proxy_url} ({proxy.region}) - {proxy.last_connect_ms}ms - {proxy.anonymity}")
        
        # 3. 分析分配逻辑
        print(f"\n📋 步骤3: 分析分配逻辑")
        
        print("当前分配策略:")
        print("- 数据库中每个IP只能有一条记录")
        print("- 内存中可以支持多个服务商共享同一个IP")
        print("- 数据库记录用于持久化，内存分配用于实际使用")
        
        # 统计各地区代理
        region_query = select(
            Proxy.region,
            func.count(Proxy.id).label('count')
        ).where(Proxy.pool == 'main').group_by(Proxy.region)
        
        region_results = await database.fetch_all(region_query)
        
        print(f"\n地区分布:")
        for row in region_results:
            region = row['region'] or 'Unknown'
            count = row['count']
            print(f"  {region}: {count} 个代理")
        
        # 4. 检查gemini地区限制
        print(f"\n🌍 步骤4: 检查gemini地区限制")
        
        cn_query = select(func.count(Proxy.id)).where(
            (Proxy.pool == 'main') & (Proxy.region == 'CN')
        )
        cn_count = await database.fetch_val(cn_query)
        
        non_cn_query = select(func.count(Proxy.id)).where(
            (Proxy.pool == 'main') & ((Proxy.region != 'CN') | (Proxy.region == None))
        )
        non_cn_count = await database.fetch_val(non_cn_query)
        
        print(f"CN地区代理: {cn_count} 个")
        print(f"非CN地区代理: {non_cn_count} 个")
        
        print(f"\n服务商可用代理分析:")
        print(f"- groq: {cn_count + non_cn_count} 个（无地区限制）")
        print(f"- vercel: {cn_count + non_cn_count} 个（无地区限制）")
        print(f"- gemini: {non_cn_count} 个（排除CN地区）")
        
        # 5. 总结
        print(f"\n🎉 总结:")
        
        total_main = sum(row['count'] for row in main_results)
        
        if total_main > 0:
            print(f"✅ 主池重建成功！")
            print(f"- 主池代理总数: {total_main}")
            print(f"- 数据库中已正确标记")
            print(f"- 支持多服务商共享（在内存中）")
            
            # 检查是否需要重启服务
            print(f"\n📝 下一步操作:")
            print(f"1. 重启代理管理器服务以加载新的主池配置")
            print(f"2. 内存池将自动从数据库加载并支持多服务商共享")
            print(f"3. 刷新网页查看最新的分配结果")
            
            print(f"\n💡 预期结果:")
            print(f"- groq: 应该显示 {cn_count + non_cn_count} 个代理")
            print(f"- vercel: 应该显示 {cn_count + non_cn_count} 个代理")
            print(f"- gemini: 应该显示 {non_cn_count} 个代理")
            print(f"- 同一个IP可以被多个服务商使用")
        else:
            print(f"❌ 主池为空，需要进一步调试")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            await database.disconnect()
        except:
            pass

if __name__ == "__main__":
    asyncio.run(check_final_status())
