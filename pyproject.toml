[project]
name = "polling"
version = "0.1.0"
description = ""
authors = [
    {name = "Your Name",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.12"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
package-mode = false
[tool.poetry.dependencies]
fastapi = "^0.111.0"
uvicorn = "^0.29.0"
sqlalchemy = "^2.0.29"
python-dotenv = "^1.0.1"
beautifulsoup4 = "^4.12.3"
requests = "^2.31.0"
apscheduler = "^3.10.4"
pydantic-settings = ">=2.10.1,<3.0.0"
databases = ">=0.9.0,<0.10.0"
google-generativeai = ">=0.8.5,<0.9.0"
openai = ">=1.100.1,<2.0.0"
starlette = ">=0.37.2,<0.38.0"
pydantic = ">=2.11.7,<3.0.0"
packaging = ">=25.0,<26.0"
alembic = "^1.16.4"
aiosqlite = "^0.21.0"
orjson = "^3.11.2"
httpx = {version = "0.28.1", optional = true}
httpcore = {version = "1.0.9", optional = true}
socksio = "1.0.0"

[tool.poetry.group.dev.dependencies]
pipdeptree = "^2.28.0"
pytest = "^8.4.1"
ruff = "^0.12.9"

[tool.deptry]
ignore = ["DEP001"]
exclude = ["conftest.py"]