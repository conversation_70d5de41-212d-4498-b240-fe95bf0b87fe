#!/usr/bin/env python3
"""
测试数据库加载功能
"""

import asyncio
from app.database.connection import database
from app.database.models import Proxy
from extensions.proxy.manager import get_proxy_manager
from sqlalchemy import select, func

async def test_db_load():
    """测试从数据库加载代理"""
    await database.connect()
    
    try:
        # 1. 检查数据库中的代理数量
        print("1. 检查数据库中的代理数量...")
        count_query = select(func.count(Proxy.id))
        total_count = await database.fetch_val(count_query)
        print(f"   数据库总代理数: {total_count}")
        
        pool_count_query = select(Proxy.pool, func.count(Proxy.id).label('count')).group_by(Proxy.pool)
        pool_counts = await database.fetch_all(pool_count_query)
        for row in pool_counts:
            print(f"   {row['pool']}池: {row['count']} 个")
        
        # 2. 重置并从数据库加载
        print("\n2. 重置内存池并从数据库加载...")
        mgr = get_proxy_manager()
        mgr.reset_pools()
        await mgr.initialize_from_db()
        
        # 3. 检查内存中的代理数量
        print("\n3. 检查内存中的代理数量...")
        main_snapshot, warm_urls, fail_urls = mgr.pools.snapshot()
        
        main_count = sum(len(urls) for partition in main_snapshot.values() for urls in partition.values())
        warm_count = len(warm_urls)
        fail_count = len(fail_urls)
        total_memory = main_count + warm_count + fail_count
        
        print(f"   内存主池: {main_count} 个")
        print(f"   内存热池: {warm_count} 个")
        print(f"   内存失败池: {fail_count} 个")
        print(f"   内存总计: {total_memory} 个")
        
        # 4. 对比结果
        print(f"\n4. 对比结果:")
        print(f"   数据库总数: {total_count}")
        print(f"   内存总数: {total_memory}")
        print(f"   差异: {total_count - total_memory}")
        
        if total_count == total_memory:
            print("   ✅ 数据一致")
        else:
            print("   ❌ 数据不一致")
            
            # 5. 分析原因
            print(f"\n5. 分析不一致原因...")
            
            # 检查是否有无效的代理URL
            invalid_query = select(Proxy.proxy_url, Proxy.pool).where(
                ~Proxy.proxy_url.like('http://%') &
                ~Proxy.proxy_url.like('https://%') &
                ~Proxy.proxy_url.like('socks5://%')
            )
            invalid_proxies = await database.fetch_all(invalid_query)
            if invalid_proxies:
                print(f"   发现 {len(invalid_proxies)} 个无效协议的代理:")
                for proxy in invalid_proxies[:5]:
                    print(f"     - {proxy['proxy_url']} ({proxy['pool']})")
                if len(invalid_proxies) > 5:
                    print(f"     ... 还有 {len(invalid_proxies) - 5} 个")
            
            # 检查是否有空URL
            empty_query = select(func.count(Proxy.id)).where(
                (Proxy.proxy_url == '') | (Proxy.proxy_url.is_(None))
            )
            empty_count = await database.fetch_val(empty_query)
            if empty_count > 0:
                print(f"   发现 {empty_count} 个空URL的代理")
        
    finally:
        await database.disconnect()

if __name__ == "__main__":
    asyncio.run(test_db_load())
