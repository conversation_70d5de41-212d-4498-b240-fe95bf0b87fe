#!/usr/bin/env python3
"""
调试热池分配问题 - 检查内存和数据库的同步问题
"""
import asyncio
import sys
import os
from datetime import datetime, timedelta
from sqlalchemy import select, update, func, text
from app.database.connection import database
from app.database.models import Proxy

async def check_database_connection():
    """检查数据库连接和表结构"""
    print("🔍 检查数据库连接和表结构...")
    
    try:
        await database.connect()
        
        # 检查表是否存在
        tables_query = text("SELECT name FROM sqlite_master WHERE type='table' AND name='t_proxies';")
        tables = await database.fetch_all(tables_query)
        
        if tables:
            print("✅ t_proxies 表存在")
            
            # 检查表结构
            schema_query = text("PRAGMA table_info(t_proxies);")
            schema = await database.fetch_all(schema_query)
            
            print("📋 表结构:")
            for column in schema:
                print(f"  {column.name}: {column.type} (nullable: {not column.notnull})")
            
            # 检查是否有任何数据
            count_query = text("SELECT COUNT(*) as count FROM t_proxies;")
            count_result = await database.fetch_one(count_query)
            print(f"📊 表中记录数: {count_result['count']}")
            
            # 如果有数据，显示一些样本
            if count_result['count'] > 0:
                sample_query = text("SELECT * FROM t_proxies LIMIT 5;")
                samples = await database.fetch_all(sample_query)
                
                print("📝 数据样本:")
                for sample in samples:
                    print(f"  ID: {sample.id}, URL: {sample.proxy_url}, Pool: {sample.pool}")
            
        else:
            print("❌ t_proxies 表不存在")
            
    except Exception as e:
        print(f"❌ 数据库连接错误: {e}")
    finally:
        await database.disconnect()

async def analyze_log_discrepancy():
    """分析日志中的差异"""
    print("\n🔍 分析日志中的热池添加记录...")
    
    # 从日志中我们看到：
    # "增量添加了 42256 个新代理到热池"
    # 但数据库中却没有数据
    
    print("📋 从日志分析:")
    print("  - 日志显示: '增量添加了 42256 个新代理到热池'")
    print("  - 数据库显示: 0 个代理")
    print("  - 这表明内存操作成功，但数据库同步失败")

async def check_manager_save_logic():
    """检查管理器的保存逻辑"""
    print("\n🔍 检查管理器保存逻辑...")
    
    # 查看manager.py中的save_to_db方法
    print("📋 可能的问题:")
    print("  1. save_to_db 方法可能没有被调用")
    print("  2. 数据库事务可能失败但没有抛出异常")
    print("  3. 批量插入可能有问题")
    print("  4. 数据库连接可能在保存时断开")

async def check_db_first_manager():
    """检查数据库优先管理器"""
    print("\n🔍 检查数据库优先管理器...")
    
    # 检查db_first_proxy_manager.py的逻辑
    print("📋 db_first_proxy_manager.py 分析:")
    print("  - add_to_warm_pool 方法应该同时更新内存和数据库")
    print("  - 如果数据库插入失败，内存中的代理仍然会被添加")
    print("  - 这可能导致内存和数据库不同步")

async def simulate_hot_pool_operation():
    """模拟热池操作来测试数据库写入"""
    print("\n🧪 模拟热池操作...")
    
    await database.connect()
    
    try:
        # 尝试插入一个测试代理
        test_proxy_data = {
            'proxy_url': 'http://test.example.com:8080',
            'pool': 'warm',
            'provider': None,
            'api_group': None,
            'region': None,
            'anonymity': None,
            'last_connect_ms': None,
            'last_e2e_ms': None,
            'last_e2e_ms_by_provider': None,
            'failure_count': 0,
            'last_checked_at': datetime.now()
        }
        
        from sqlalchemy import insert
        insert_query = insert(Proxy).values(**test_proxy_data)
        result = await database.execute(insert_query)
        
        if result:
            print("✅ 测试代理插入成功")
            
            # 验证插入
            verify_query = select(Proxy).where(Proxy.proxy_url == 'http://test.example.com:8080')
            verify_result = await database.fetch_one(verify_query)
            
            if verify_result:
                print("✅ 测试代理验证成功")
                
                # 清理测试数据
                from sqlalchemy import delete
                delete_query = delete(Proxy).where(Proxy.proxy_url == 'http://test.example.com:8080')
                await database.execute(delete_query)
                print("🧹 测试数据已清理")
            else:
                print("❌ 测试代理验证失败")
        else:
            print("❌ 测试代理插入失败")
            
    except Exception as e:
        print(f"❌ 模拟操作失败: {e}")
    finally:
        await database.disconnect()

async def check_application_startup():
    """检查应用启动时的代理池加载"""
    print("\n🔍 检查应用启动时的代理池加载...")
    
    print("📋 从日志分析应用启动:")
    print("  - '正在从数据库加载代理池状态...'")
    print("  - '代理池状态已从数据库加载完成'")
    print("  - 但随后显示 '增量添加了 42256 个新代理到热池'")
    print("  - 这表明:")
    print("    1. 数据库中原本没有代理数据")
    print("    2. 系统从外部源获取了代理")
    print("    3. 代理被添加到内存热池")
    print("    4. 但可能没有正确保存到数据库")

async def main():
    """主函数"""
    print("🚀 开始调试热池分配问题...")
    
    await check_database_connection()
    await analyze_log_discrepancy()
    await check_manager_save_logic()
    await check_db_first_manager()
    await simulate_hot_pool_operation()
    await check_application_startup()
    
    print("\n📋 问题总结:")
    print("1. 数据库表结构正常，但没有代理数据")
    print("2. 日志显示内存中添加了42256个代理到热池")
    print("3. 这表明内存操作成功，但数据库同步失败")
    print("4. 可能的原因:")
    print("   - save_to_db 方法没有被调用")
    print("   - 数据库事务失败但没有抛出异常")
    print("   - 批量插入逻辑有问题")
    print("   - 数据库连接在保存时断开")
    
    print("\n🔧 建议的修复步骤:")
    print("1. 检查 manager.py 中的 save_to_db 调用")
    print("2. 在数据库操作中添加更多错误处理和日志")
    print("3. 确保事务正确提交")
    print("4. 添加数据库同步验证")
    
    print("\n✅ 调试完成!")

if __name__ == "__main__":
    asyncio.run(main())
