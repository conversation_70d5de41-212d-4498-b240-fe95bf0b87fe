import httpx
import sys
import asyncio

async def check_proxy_ip(proxy_url: str):
    """
    Checks the public IP address when making a request through a given proxy.
    
    Args:
        proxy_url: The URL of the proxy to test (e.g., *********************:port).
    """
    # URL of a service that returns the request's origin IP address
    ip_check_service = "https://httpbin.org/ip"
    print(f"Testing proxy: {proxy_url}")
    print(f"Querying IP from: {ip_check_service}")

    # Set up proxies for httpx. It needs a dictionary.
    # The key 'all://' applies the proxy to all schemes (http, https).
    proxies = {
        "all://": proxy_url,
    }

    # Define a reasonable timeout
    timeout = httpx.Timeout(15.0, connect=10.0)

    try:
        async with httpx.AsyncClient(proxies=proxies, timeout=timeout, verify=False) as client:
            response = await client.get(ip_check_service)
            response.raise_for_status()  # Raise an exception for bad status codes (4xx or 5xx)
            
            # The service returns a JSON with the origin IP
            data = response.json()
            public_ip = data.get("origin")
            
            print("\n--- Test Result ---")
            if public_ip:
                print(f"✅ Success! The public IP seen by the server is: {public_ip}")
                print("Compare this with your server's real IP. If they are different, the proxy is working correctly.")
            else:
                print("⚠️ Could not determine the public IP. The response was: {data}")

    except httpx.ProxyError as e:
        print("\n--- Test Result ---")
        print("❌ Error: Failed to connect to the proxy.")
        print(f"   Please check if the proxy URL '{proxy_url}' is correct and the proxy server is running.")
        print(f"   Details: {e}")
    except httpx.RequestError as e:
        print("\n--- Test Result ---")
        print("❌ Error: A request error occurred (e.g., DNS failure, connection refused).")
        print(f"   Details: {e}")
    except Exception as e:
        print("\n--- Test Result ---")
        print(f"❌ An unexpected error occurred: {e}")


def main():
    if len(sys.argv) != 2:
        print("Usage: .venv/bin/python test_proxy.py <proxy_url>")
        print("Example: .venv/bin/python test_proxy.py http://127.0.0.1:7890")
        sys.exit(1)
        
    proxy_url_arg = sys.argv[1]
    asyncio.run(check_proxy_ip(proxy_url_arg))

if __name__ == "__main__":
    main()
