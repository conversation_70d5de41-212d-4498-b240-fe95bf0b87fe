#!/usr/bin/env python3
"""
修复数据库中"error"服务商的问题
"""

import asyncio
from app.database.connection import database
from app.database.models import Proxy
from sqlalchemy import select, update, func

async def fix_error_provider():
    """修复数据库中的error服务商问题"""
    await database.connect()
    
    try:
        print("=== 修复数据库中的'error'服务商问题 ===\n")
        
        # 1. 统计当前的"error"服务商数量
        print("📊 步骤1: 检查当前'error'服务商数量")
        error_count_query = select(func.count(Proxy.id)).where(Proxy.provider == 'error')
        error_count = await database.fetch_val(error_count_query)
        
        print(f"发现 {error_count} 个'error'服务商的代理")
        
        if error_count == 0:
            print("✅ 没有发现'error'服务商，无需修复")
            return
        
        # 2. 显示一些"error"服务商的详情
        print(f"\n🔍 步骤2: 显示'error'服务商详情（前10个）")
        error_details_query = select(Proxy).where(Proxy.provider == 'error').limit(10)
        error_proxies = await database.fetch_all(error_details_query)
        
        for i, proxy in enumerate(error_proxies):
            print(f"  {i+1}. {proxy.proxy_url}")
            print(f"     池: {proxy.pool}")
            print(f"     API组: {proxy.api_group}")
            print(f"     地区: {proxy.region}")
            print(f"     匿名性: {proxy.anonymity}")
            print()
        
        # 3. 修复策略
        print(f"🔧 步骤3: 修复策略")
        print("对于'error'服务商的代理，我们将：")
        print("1. 如果在主池中：将provider和api_group设置为NULL")
        print("2. 如果在warm/fail池中：将provider和api_group设置为NULL（这些池不应该有provider）")
        
        # 4. 执行修复
        print(f"\n⚡ 步骤4: 执行修复")
        
        # 修复主池中的"error"服务商
        main_error_query = select(func.count(Proxy.id)).where(
            (Proxy.provider == 'error') & (Proxy.pool == 'main')
        )
        main_error_count = await database.fetch_val(main_error_query)
        
        if main_error_count > 0:
            print(f"修复主池中的 {main_error_count} 个'error'服务商代理...")
            main_fix_query = update(Proxy).where(
                (Proxy.provider == 'error') & (Proxy.pool == 'main')
            ).values(
                provider=None,
                api_group=None
            )
            await database.execute(main_fix_query)
            print(f"✅ 主池修复完成")
        
        # 修复其他池中的"error"服务商
        other_error_query = select(func.count(Proxy.id)).where(
            (Proxy.provider == 'error') & (Proxy.pool != 'main')
        )
        other_error_count = await database.fetch_val(other_error_query)
        
        if other_error_count > 0:
            print(f"修复其他池中的 {other_error_count} 个'error'服务商代理...")
            other_fix_query = update(Proxy).where(
                (Proxy.provider == 'error') & (Proxy.pool != 'main')
            ).values(
                provider=None,
                api_group=None
            )
            await database.execute(other_fix_query)
            print(f"✅ 其他池修复完成")
        
        # 5. 验证修复结果
        print(f"\n✅ 步骤5: 验证修复结果")
        
        # 检查是否还有"error"服务商
        remaining_error_count = await database.fetch_val(error_count_query)
        print(f"修复后剩余'error'服务商: {remaining_error_count}")
        
        if remaining_error_count == 0:
            print("🎉 所有'error'服务商已成功修复！")
        else:
            print(f"⚠️  仍有 {remaining_error_count} 个'error'服务商未修复")
        
        # 6. 统计修复后的服务商分布
        print(f"\n📊 步骤6: 修复后的服务商分布")
        provider_stats_query = select(
            Proxy.pool, 
            Proxy.provider, 
            func.count(Proxy.id).label('count')
        ).group_by(Proxy.pool, Proxy.provider)
        
        provider_stats = await database.fetch_all(provider_stats_query)
        
        pool_stats = {}
        for row in provider_stats:
            pool = row['pool'] or 'unknown'
            provider = row['provider'] or 'NULL'
            count = row['count']
            
            if pool not in pool_stats:
                pool_stats[pool] = {}
            pool_stats[pool][provider] = count
        
        for pool, providers in pool_stats.items():
            print(f"\n{pool}池:")
            for provider, count in providers.items():
                print(f"  {provider}: {count:,} 个代理")
        
        # 7. 建议
        print(f"\n💡 建议:")
        print("1. 重启代理管理器以重新加载数据库状态")
        print("2. 运行主池构建以重新分配服务商")
        print("3. 监控日志确保不再出现'error'服务商")
        
        print(f"\n🎯 修复总结:")
        print(f"- 修复前'error'服务商: {error_count}")
        print(f"- 修复后'error'服务商: {remaining_error_count}")
        print(f"- 成功修复: {error_count - remaining_error_count}")
        
    finally:
        await database.disconnect()

async def main():
    """主函数"""
    await fix_error_provider()

if __name__ == "__main__":
    asyncio.run(main())
