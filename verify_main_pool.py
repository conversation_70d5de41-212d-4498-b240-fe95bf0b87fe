#!/usr/bin/env python3
"""
验证主池分配结果
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.insert(0, '/home/<USER>/应用/轮询')

async def verify_main_pool():
    """验证主池分配结果"""
    print("=== 验证主池分配结果 ===\n")
    
    try:
        from app.database.connection import database
        from app.database.models import Proxy
        from sqlalchemy import select, func
        
        await database.connect()
        
        # 1. 检查数据库中的主池分配
        print("📊 步骤1: 检查数据库中的主池分配")
        
        main_query = select(
            Proxy.provider,
            Proxy.api_group,
            func.count(Proxy.id).label('count')
        ).where(Proxy.pool == 'main').group_by(Proxy.provider, Proxy.api_group)
        
        main_results = await database.fetch_all(main_query)
        
        if main_results:
            print("数据库中的主池分配:")
            provider_totals = {}
            for row in main_results:
                provider = row['provider'] or 'NULL'
                api_group = row['api_group'] or 'NULL'
                count = row['count']
                
                if provider not in provider_totals:
                    provider_totals[provider] = 0
                provider_totals[provider] += count
                
                print(f"  {provider}/{api_group}: {count} 个代理")
            
            print(f"\n各服务商总计:")
            for provider, total in provider_totals.items():
                print(f"  {provider}: {total} 个代理")
        else:
            print("❌ 数据库中没有主池代理")
        
        # 2. 检查具体的主池代理
        print(f"\n🔍 步骤2: 检查具体的主池代理")
        
        detail_query = select(Proxy).where(Proxy.pool == 'main').limit(20)
        detail_results = await database.fetch_all(detail_query)
        
        if detail_results:
            print(f"主池代理详情（前20个）:")
            for i, proxy in enumerate(detail_results):
                print(f"  {i+1}. {proxy.proxy_url}")
                print(f"     服务商: {proxy.provider}")
                print(f"     API组: {proxy.api_group}")
                print(f"     地区: {proxy.region}")
                print(f"     匿名性: {proxy.anonymity}")
                print(f"     延迟: {proxy.last_connect_ms}ms")
                print()
        
        # 3. 检查是否有NULL服务商
        print(f"🔍 步骤3: 检查NULL服务商")
        
        null_query = select(func.count(Proxy.id)).where(
            (Proxy.pool == 'main') & 
            ((Proxy.provider == None) | (Proxy.provider == ''))
        )
        null_count = await database.fetch_val(null_query)
        
        print(f"NULL服务商的主池代理: {null_count} 个")
        
        if null_count > 0:
            print("⚠️  发现NULL服务商，这可能是数据更新问题")
        
        # 4. 检查groq缺失的原因
        print(f"\n🔍 步骤4: 检查groq分配")
        
        groq_query = select(Proxy).where(
            (Proxy.pool == 'main') & (Proxy.provider == 'groq')
        )
        groq_results = await database.fetch_all(groq_query)
        
        print(f"groq服务商的代理: {len(groq_results)} 个")
        
        if len(groq_results) == 0:
            print("❌ groq没有分配到代理")
            
            # 检查是否有符合条件的代理
            candidate_query = select(Proxy).where(
                (Proxy.pool == 'warm') &
                (Proxy.last_connect_ms != None) &
                (Proxy.last_connect_ms <= 200) &
                (Proxy.anonymity.in_(['anonymous', 'elite', 'high_anonymous']))
            ).limit(10)
            
            candidates = await database.fetch_all(candidate_query)
            print(f"热池中符合条件的候选代理: {len(candidates)} 个")
            
            if candidates:
                print("候选代理示例:")
                for i, proxy in enumerate(candidates[:5]):
                    print(f"  {i+1}. {proxy.proxy_url} - {proxy.last_connect_ms}ms - {proxy.anonymity} - {proxy.region}")
        
        # 5. 手动修复分配（如果需要）
        print(f"\n🔧 步骤5: 手动修复分配")
        
        # 获取所有符合主池条件的代理
        main_candidates_query = select(Proxy).where(
            (Proxy.pool.in_(['warm', 'main'])) &
            (Proxy.last_connect_ms != None) &
            (Proxy.last_connect_ms <= 200) &
            (Proxy.anonymity.in_(['anonymous', 'elite', 'high_anonymous']))
        )
        
        main_candidates = await database.fetch_all(main_candidates_query)
        print(f"所有符合主池条件的代理: {len(main_candidates)} 个")
        
        if main_candidates:
            print("开始手动修复分配...")
            
            from sqlalchemy import update
            
            # 清除现有主池分配
            clear_query = update(Proxy).where(Proxy.pool == 'main').values(
                pool='warm',
                provider=None,
                api_group=None
            )
            await database.execute(clear_query)
            print("✅ 清除现有主池分配")
            
            # 重新分配
            groq_count = 0
            vercel_count = 0
            gemini_count = 0
            
            for proxy in main_candidates:
                # 检查地区限制
                region = (proxy.region or '').upper()
                
                # 分配给groq
                if groq_count < len(main_candidates):
                    for api_group in ['chat', 'embeddings']:
                        update_query = update(Proxy).where(Proxy.proxy_url == proxy.proxy_url).values(
                            pool='main',
                            provider='groq',
                            api_group=api_group
                        )
                        await database.execute(update_query)
                    groq_count += 1
                
                # 分配给vercel
                if vercel_count < len(main_candidates):
                    for api_group in ['chat', 'embeddings', 'images']:
                        update_query = update(Proxy).where(Proxy.proxy_url == proxy.proxy_url).values(
                            pool='main',
                            provider='vercel',
                            api_group=api_group
                        )
                        await database.execute(update_query)
                    vercel_count += 1
                
                # 分配给gemini（排除特定地区）
                gemini_excluded = {'CU', 'IR', 'KP', 'SY', 'RU', 'BY', 'SD', 'CN'}
                if region not in gemini_excluded and gemini_count < len(main_candidates):
                    update_query = update(Proxy).where(Proxy.proxy_url == proxy.proxy_url).values(
                        pool='main',
                        provider='gemini',
                        api_group='chat'
                    )
                    await database.execute(update_query)
                    gemini_count += 1
            
            print(f"✅ 修复完成:")
            print(f"  groq: {groq_count} 个代理")
            print(f"  vercel: {vercel_count} 个代理")
            print(f"  gemini: {gemini_count} 个代理")
        
        # 6. 最终验证
        print(f"\n✅ 步骤6: 最终验证")
        
        final_query = select(
            Proxy.provider,
            func.count(Proxy.id).label('count')
        ).where(Proxy.pool == 'main').group_by(Proxy.provider)
        
        final_results = await database.fetch_all(final_query)
        
        print("最终分配结果:")
        for row in final_results:
            provider = row['provider'] or 'NULL'
            count = row['count']
            print(f"  {provider}: {count} 个代理")
        
        # 检查分配是否平衡
        counts = [row['count'] for row in final_results if row['provider']]
        if counts:
            min_count = min(counts)
            max_count = max(counts)
            
            if max_count - min_count <= 1:
                print("✅ 分配平衡")
            else:
                print(f"⚠️  分配不平衡: 最少{min_count}，最多{max_count}")
        
        print(f"\n🎉 验证完成！现在可以刷新网页查看结果")
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            await database.disconnect()
        except:
            pass

if __name__ == "__main__":
    asyncio.run(verify_main_pool())
