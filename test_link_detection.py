#!/usr/bin/env python3
"""
测试修复后的链接识别逻辑
"""

def is_txt_proxy_source(url: str, link_text: str = "") -> bool:
    """判断URL是否是TXT代理源"""
    url_lower = url.lower()
    text_lower = link_text.lower()

    # 检查文件扩展名
    if url_lower.endswith('.txt'):
        # 检查是否包含代理相关关键词
        proxy_keywords = ['proxy', 'socks', 'http', 'https', 'ip', 'list']
        if any(keyword in url_lower or keyword in text_lower for keyword in proxy_keywords):
            return True

    # 检查GitHub raw文件
    if 'raw.githubusercontent.com' in url_lower and '.txt' in url_lower:
        proxy_keywords = ['proxy', 'socks', 'http', 'ip']
        if any(keyword in url_lower for keyword in proxy_keywords):
            return True

    return False


def is_api_proxy_source(url: str, link_text: str = "") -> bool:
    """判断URL是否是API代理源"""
    url_lower = url.lower()
    text_lower = link_text.lower()

    # 排除GitHub页面链接
    if 'github.com' in url_lower and not 'raw.githubusercontent.com' in url_lower:
        return False
    
    # 排除HTML页面
    if url_lower.endswith('.html') or url_lower.endswith('.htm'):
        return False
    
    # 排除明显的网页链接
    if any(pattern in url_lower for pattern in ['#', '?q=', '/topics/', '/search']):
        return False

    # 检查真正的API端点
    api_indicators = [
        # 明确的API路径
        '/api/proxy',
        '/api/proxies', 
        '/api/get',
        '/api/list',
        # JSON端点
        '.json',
        # 动态页面（可能返回代理数据）
        '.php?',
        '.asp?',
        '.jsp?',
        # 专门的代理API
        '/proxy.txt',
        '/proxies.txt',
        '/get.php',
        '/list.php'
    ]

    # 必须匹配API指示器
    if any(indicator in url_lower for indicator in api_indicators):
        # 并且包含代理相关关键词
        proxy_keywords = ['proxy', 'socks', 'ip']
        if any(keyword in url_lower for keyword in proxy_keywords):
            return True

    return False


def test_link_detection():
    """测试链接识别逻辑"""
    print("=== 测试修复后的链接识别逻辑 ===\n")
    
    test_cases = [
        # TXT源测试
        {
            "url": "https://raw.githubusercontent.com/user/repo/main/proxy.txt",
            "expected_txt": True,
            "expected_api": False,
            "description": "GitHub raw TXT文件"
        },
        {
            "url": "https://example.com/proxies.txt",
            "expected_txt": True,
            "expected_api": False,
            "description": "普通TXT文件"
        },
        {
            "url": "https://site.com/proxy-list.txt",
            "expected_txt": True,
            "expected_api": False,
            "description": "代理列表TXT文件"
        },
        
        # API源测试
        {
            "url": "https://api.example.com/proxy.json",
            "expected_txt": False,
            "expected_api": True,
            "description": "JSON API端点"
        },
        {
            "url": "https://example.com/api/proxies",
            "expected_txt": False,
            "expected_api": True,
            "description": "API代理端点"
        },
        {
            "url": "https://site.com/get.php?type=proxy",
            "expected_txt": False,
            "expected_api": True,
            "description": "PHP动态代理页面"
        },
        
        # 应该被排除的链接
        {
            "url": "https://github.com/user/repo",
            "expected_txt": False,
            "expected_api": False,
            "description": "GitHub仓库页面"
        },
        {
            "url": "https://github.com/topics/proxy",
            "expected_txt": False,
            "expected_api": False,
            "description": "GitHub主题页面"
        },
        {
            "url": "https://github.com/search?q=proxy+list",
            "expected_txt": False,
            "expected_api": False,
            "description": "GitHub搜索页面"
        },
        {
            "url": "https://example.com/page.html",
            "expected_txt": False,
            "expected_api": False,
            "description": "HTML页面"
        },
        {
            "url": "https://site.com/proxy#section",
            "expected_txt": False,
            "expected_api": False,
            "description": "带锚点的页面"
        },
        
        # 边界情况
        {
            "url": "https://example.com/data.txt",
            "expected_txt": False,
            "expected_api": False,
            "description": "不相关的TXT文件"
        },
        {
            "url": "https://api.example.com/users.json",
            "expected_txt": False,
            "expected_api": False,
            "description": "不相关的JSON API"
        }
    ]
    
    print("🔍 链接识别测试结果:")
    print("-" * 80)
    
    passed = 0
    failed = 0
    
    for i, case in enumerate(test_cases, 1):
        url = case["url"]
        expected_txt = case["expected_txt"]
        expected_api = case["expected_api"]
        description = case["description"]
        
        actual_txt = is_txt_proxy_source(url)
        actual_api = is_api_proxy_source(url)
        
        txt_correct = actual_txt == expected_txt
        api_correct = actual_api == expected_api
        overall_correct = txt_correct and api_correct
        
        status = "✅ PASS" if overall_correct else "❌ FAIL"
        
        print(f"{i:2d}. {status} {description}")
        print(f"    URL: {url}")
        print(f"    TXT: 期望={expected_txt}, 实际={actual_txt} {'✓' if txt_correct else '✗'}")
        print(f"    API: 期望={expected_api}, 实际={actual_api} {'✓' if api_correct else '✗'}")
        print()
        
        if overall_correct:
            passed += 1
        else:
            failed += 1
    
    print("=" * 80)
    print(f"📊 测试总结:")
    print(f"   总测试用例: {len(test_cases)}")
    print(f"   通过: {passed}")
    print(f"   失败: {failed}")
    print(f"   成功率: {passed/len(test_cases)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 所有测试用例都通过了！链接识别逻辑修复成功！")
    else:
        print(f"\n⚠️  有 {failed} 个测试用例失败，需要进一步调整逻辑")
    
    print("\n🎯 修复效果:")
    print("- ✅ 正确识别TXT代理源")
    print("- ✅ 正确识别API代理源") 
    print("- ✅ 排除GitHub页面链接")
    print("- ✅ 排除HTML页面和无关链接")
    print("- ✅ 避免误识别问题")


if __name__ == "__main__":
    test_link_detection()
