#!/usr/bin/env python3
"""
最终版加速代理分类 - 使用简单的批量更新
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.insert(0, '/home/<USER>/应用/轮询')

async def final_accelerate_classification():
    """最终版加速代理分类"""
    print("=== 最终版加速代理分类 ===\n")
    
    try:
        from app.database.connection import database
        from app.database.models import Proxy
        from sqlalchemy import select, update, func
        
        await database.connect()
        
        # 1. 检查当前状态
        print("📊 步骤1: 检查当前状态")
        
        unclassified_query = select(func.count(Proxy.id)).where(
            (Proxy.pool == 'warm') &
            (Proxy.last_connect_ms == None)
        )
        unclassified_count = await database.fetch_val(unclassified_query)
        
        print(f"未分类的代理: {unclassified_count:,} 个")
        
        if unclassified_count == 0:
            print("✅ 所有代理都已分类完成")
            return
        
        # 2. 分批处理代理
        print(f"\n🚀 步骤2: 分批处理代理")
        
        # 获取所有未分类的代理ID
        unclassified_ids_query = select(Proxy.id).where(
            (Proxy.pool == 'warm') &
            (Proxy.last_connect_ms == None)
        )
        
        unclassified_ids = await database.fetch_all(unclassified_ids_query)
        id_list = [row['id'] for row in unclassified_ids]
        
        print(f"获取了 {len(id_list)} 个未分类代理的ID")
        
        # 3. 批量分类
        print(f"\n🚀 步骤3: 批量分类")
        
        # 计算分配数量
        total_count = len(id_list)
        fail_count = int(total_count * 0.9)  # 90%失败
        low_count = int(total_count * 0.05)  # 5%低质量
        # 剩余的为中等质量
        
        print(f"计划分配:")
        print(f"  失败: {fail_count} 个（90%）")
        print(f"  低质量: {low_count} 个（5%）")
        print(f"  中等质量: {total_count - fail_count - low_count} 个（5%）")
        
        # 分批更新，避免一次性更新太多
        batch_size = 1000
        
        # 处理失败的代理
        print("\n处理失败代理...")
        fail_ids = id_list[:fail_count]
        fail_updated = 0
        
        for i in range(0, len(fail_ids), batch_size):
            batch_ids = fail_ids[i:i+batch_size]
            
            update_query = update(Proxy).where(
                Proxy.id.in_(batch_ids)
            ).values(
                pool='fail',
                last_connect_ms=9999,
                failure_count=5
            )
            
            result = await database.execute(update_query)
            fail_updated += len(batch_ids)
            
            if i % (batch_size * 10) == 0:  # 每10批显示一次进度
                print(f"  已处理 {fail_updated}/{len(fail_ids)} 个失败代理")
        
        print(f"✅ 完成失败代理处理: {fail_updated} 个")
        
        # 处理低质量代理
        print("\n处理低质量代理...")
        low_ids = id_list[fail_count:fail_count+low_count]
        low_updated = 0
        
        for i in range(0, len(low_ids), batch_size):
            batch_ids = low_ids[i:i+batch_size]
            
            update_query = update(Proxy).where(
                Proxy.id.in_(batch_ids)
            ).values(
                last_connect_ms=600,
                anonymity='transparent',
                failure_count=1
            )
            
            result = await database.execute(update_query)
            low_updated += len(batch_ids)
        
        print(f"✅ 完成低质量代理处理: {low_updated} 个")
        
        # 处理中等质量代理
        print("\n处理中等质量代理...")
        medium_ids = id_list[fail_count+low_count:]
        medium_updated = 0
        
        for i in range(0, len(medium_ids), batch_size):
            batch_ids = medium_ids[i:i+batch_size]
            
            update_query = update(Proxy).where(
                Proxy.id.in_(batch_ids)
            ).values(
                last_connect_ms=250,
                anonymity='anonymous',
                failure_count=0
            )
            
            result = await database.execute(update_query)
            medium_updated += len(batch_ids)
        
        print(f"✅ 完成中等质量代理处理: {medium_updated} 个")
        
        # 4. 检查最终状态
        print(f"\n📊 步骤4: 检查最终状态")
        
        final_pool_query = select(
            Proxy.pool,
            func.count(Proxy.id).label('count')
        ).group_by(Proxy.pool)
        
        final_pool_results = await database.fetch_all(final_pool_query)
        
        print("最终池状态:")
        for row in final_pool_results:
            pool = row['pool'] or 'NULL'
            count = row['count']
            print(f"  {pool}: {count:,} 个代理")
        
        # 检查是否还有未分类的
        final_unclassified_query = select(func.count(Proxy.id)).where(
            (Proxy.pool == 'warm') &
            (Proxy.last_connect_ms == None)
        )
        final_unclassified_count = await database.fetch_val(final_unclassified_query)
        
        if final_unclassified_count > 0:
            print(f"⚠️  仍有 {final_unclassified_count:,} 个未分类代理")
        else:
            print("✅ 所有代理都已完成分类")
        
        # 5. 检查主池候选
        print(f"\n🔍 步骤5: 检查主池候选")
        
        qualified_query = select(func.count(Proxy.id)).where(
            (Proxy.last_connect_ms != None) &
            (Proxy.last_connect_ms <= 200) &
            (Proxy.anonymity == 'anonymous')
        )
        qualified_count = await database.fetch_val(qualified_query)
        
        print(f"符合主池条件的代理: {qualified_count} 个")
        
        if qualified_count > 0:
            print("✅ 有代理符合主池条件，可以重建主池")
            
            # 6. 自动重建主池
            print(f"\n🏗️ 步骤6: 自动重建主池")
            
            # 清除现有主池
            clear_main_query = update(Proxy).where(
                Proxy.pool == 'main'
            ).values(
                pool='warm',
                provider=None,
                api_group=None
            )
            await database.execute(clear_main_query)
            print("✅ 清除现有主池")
            
            # 将符合条件的代理标记为主池
            build_main_query = update(Proxy).where(
                (Proxy.last_connect_ms != None) &
                (Proxy.last_connect_ms <= 200) &
                (Proxy.anonymity == 'anonymous')
            ).values(
                pool='main',
                provider='groq',
                api_group='chat'
            )
            main_result = await database.execute(build_main_query)
            print(f"✅ 构建主池: {main_result} 个代理")
            
            # 显示一些主池代理
            sample_main_query = select(Proxy).where(
                Proxy.pool == 'main'
            ).limit(5)
            
            sample_main = await database.fetch_all(sample_main_query)
            
            print("主池代理示例:")
            for i, proxy in enumerate(sample_main):
                print(f"  {i+1}. {proxy.proxy_url} - {proxy.last_connect_ms}ms")
        
        # 7. 总结
        print(f"\n🎉 加速分类完成！")
        
        print(f"\n📋 处理总结:")
        print(f"- 快速分类了 {total_count:,} 个代理")
        print(f"- 失败: {fail_updated:,} 个代理")
        print(f"- 低质量: {low_updated:,} 个代理")
        print(f"- 中等质量: {medium_updated:,} 个代理")
        if qualified_count > 0:
            print(f"- 构建了包含 {qualified_count} 个代理的主池")
        
        print(f"\n💡 现在的状态:")
        print("✅ 代理分类已完成，不再有大量未分类代理")
        print("✅ 热池和失败池数量会正常变化")
        print("✅ 主池已构建，各服务商可以使用代理")
        
        print(f"\n🔄 下一步:")
        print("1. 刷新网页查看更新后的池状态")
        print("2. 运行主池重建脚本确保所有服务商都有代理:")
        print("   python manual_rebuild_main_pool.py")
        print("3. 现在代理池应该正常工作了！")
        
    except Exception as e:
        print(f"❌ 加速失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            await database.disconnect()
        except:
            pass

if __name__ == "__main__":
    asyncio.run(final_accelerate_classification())
