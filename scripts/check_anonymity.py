#!/usr/bin/env python3
from __future__ import annotations
import argparse
import asyncio
import csv
import json
import os
import sqlite3
import sys
from typing import List, Optional, Tuple

# Standalone anonymity checker (no project imports)

IP_CHECK_URLS = [
    ("https://api.ipify.org", {"format": "text"}, "text"),
    ("https://icanhazip.com", {}, "text"),
    ("https://httpbin.org/ip", {}, "json"),
]

SUPPORTED_PREFIX = ("http://", "https://", "socks5://")


async def get_direct_ip(timeout_s: int = 10) -> Optional[str]:
    import httpx

    timeout = httpx.Timeout(connect=timeout_s, read=timeout_s, write=timeout_s, pool=timeout_s)
    async with httpx.AsyncClient(timeout=timeout, verify=False, follow_redirects=True) as client:
        for url, params, resp_type in IP_CHECK_URLS:
            try:
                r = await client.get(url, params=params)
                if r.status_code != 200:
                    continue
                if resp_type == "text":
                    ip = (r.text or "").strip()
                else:
                    ip = (r.json() or {}).get("origin", "").split(",")[0].strip()
                if ip:
                    return ip
            except Exception:
                continue
    return None


async def get_ip_via_proxy(proxy_url: str, timeout_s: int = 10) -> Optional[str]:
    import httpx

    if not isinstance(proxy_url, str) or not proxy_url.strip().lower().startswith(SUPPORTED_PREFIX):
        return None
    timeout = httpx.Timeout(connect=timeout_s, read=timeout_s, write=timeout_s, pool=timeout_s)
    async with httpx.AsyncClient(timeout=timeout, verify=False, follow_redirects=True, proxies=proxy_url) as client:
        for url, params, resp_type in IP_CHECK_URLS:
            try:
                r = await client.get(url, params=params)
                if r.status_code != 200:
                    continue
                if resp_type == "text":
                    ip = (r.text or "").strip()
                else:
                    ip = (r.json() or {}).get("origin", "").split(",")[0].strip()
                if ip:
                    return ip
            except Exception:
                continue
    return None


async def check_one(proxy_url: str, direct_ip: Optional[str], timeout_s: int) -> Tuple[str, str]:
    """
    Returns tuple (proxy_url, result) where result in {anonymous, transparent, unknown}
    """
    try:
        via = await get_ip_via_proxy(proxy_url, timeout_s=timeout_s)
        if via and direct_ip:
            if via == direct_ip:
                return proxy_url, "transparent"
            else:
                return proxy_url, "anonymous"
        elif via and not direct_ip:
            # Can't determine direct IP; if via IP exists, we at least know it's going somewhere
            return proxy_url, "unknown"
        else:
            return proxy_url, "unknown"
    except Exception:
        return proxy_url, "unknown"


def _dedup_supported(urls: List[str]) -> List[str]:
    urls = [u for u in urls if isinstance(u, str) and u.strip().lower().startswith(SUPPORTED_PREFIX)]
    seen = set()
    out: List[str] = []
    for u in urls:
        if u not in seen:
            seen.add(u)
            out.append(u)
    return out


def _find_proxy_table(cur: sqlite3.Cursor) -> Tuple[Optional[str], bool]:
    """Return (table_name, has_pool_column). Tries to locate a table with proxy_url column.
    Prefers tables that also have a pool column.
    """
    cur.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [r[0] for r in cur.fetchall()]
    candidate = None
    has_pool = False
    for name in tables:
        try:
            cur.execute(f"PRAGMA table_info({name})")
            cols = {row[1] for row in cur.fetchall()}  # row[1] is column name
            if "proxy_url" in cols:
                if "pool" in cols and not candidate:
                    return name, True
                # keep the first table with proxy_url in case no pool exists anywhere
                if not candidate:
                    candidate = name
                    has_pool = "pool" in cols
        except Exception:
            continue
    return candidate, has_pool


def load_warm_proxies(db_path: str, limit: int) -> List[str]:
    if os.path.exists(db_path):
        conn = sqlite3.connect(db_path)
        try:
            cur = conn.cursor()
            table, has_pool = _find_proxy_table(cur)
            if table:
                if has_pool:
                    cur.execute(f"SELECT proxy_url FROM {table} WHERE pool = 'warm' LIMIT ?", (int(limit),))
                else:
                    cur.execute(f"SELECT proxy_url FROM {table} LIMIT ?", (int(limit),))
                rows = cur.fetchall()
                return _dedup_supported([r[0] for r in rows])
        finally:
            conn.close()

    # Fallback 1: read extensions/proxy/proxies_state.json (new format)
    try:
        state_path = os.path.join("extensions", "proxy", "proxies_state.json")
        if os.path.exists(state_path):
            with open(state_path, "r", encoding="utf-8") as f:
                st = json.load(f) or {}
            warm = st.get("warm")
            if isinstance(warm, list) and warm:
                return _dedup_supported(warm)[:limit]
            # older format: metrics-only map
            if isinstance(st, dict) and st and "warm" not in st and "fail" not in st and "main" not in st:
                return _dedup_supported(list(st.keys()))[:limit]
    except Exception:
        pass

    # Fallback 2: read extensions/proxy/proxies_cache.json (list of urls)
    try:
        cache_path = os.path.join("extensions", "proxy", "proxies_cache.json")
        if os.path.exists(cache_path):
            with open(cache_path, "r", encoding="utf-8") as f:
                arr = json.load(f) or []
            if isinstance(arr, list):
                return _dedup_supported(arr)[:limit]
    except Exception:
        pass

    return []


async def main() -> int:
    parser = argparse.ArgumentParser(description="Standalone anonymity checker for warm pool proxies")
    parser.add_argument("--db", default=os.path.join("data", "app.db"), help="Path to SQLite DB (default: data/app.db). If not found or table missing, the script falls back to JSON caches.")
    parser.add_argument("--from-file", default=None, help="Optional path to a text file (one proxy per line) to override DB/caches")
    parser.add_argument("--limit", type=int, default=500, help="Number of warm proxies to test (default: 500)")
    parser.add_argument("--timeout", type=int, default=10, help="Per-request timeout seconds (default: 10)")
    parser.add_argument("--concurrency", type=int, default=100, help="Max concurrent checks (default: 100)")
    parser.add_argument("--out", default=os.path.join("scripts", "anonymity_results.json"), help="Output JSON path")
    args = parser.parse_args()

    if args.from_file:
        if not os.path.exists(args.from_file):
            raise SystemExit(f"--from-file not found: {args.from_file}")
        with open(args.from_file, "r", encoding="utf-8") as f:
            urls = _dedup_supported([line.strip() for line in f if line.strip()])[: args.limit]
    else:
        urls = load_warm_proxies(args.db, args.limit)
    if not urls:
        print("No warm proxies found to test.")
        return 1

    print(f"Loaded {len(urls)} warm proxies from {args.db}")

    direct_ip = await get_direct_ip(timeout_s=args.timeout)
    if direct_ip:
        print(f"Direct IP: {direct_ip}")
    else:
        print("Warning: Could not determine direct IP; results may be 'unknown'.")

    sem = asyncio.Semaphore(max(1, int(args.concurrency)))

    async def wrapped(u: str):
        async with sem:
            return await check_one(u, direct_ip, args.timeout)

    results = await asyncio.gather(*[wrapped(u) for u in urls])

    # Summarize
    counts = {"anonymous": 0, "transparent": 0, "unknown": 0}
    for _, r in results:
        counts[r] = counts.get(r, 0) + 1

    print("Summary:")
    for k in ("anonymous", "transparent", "unknown"):
        print(f"  {k}: {counts.get(k, 0)}")

    data = [{"url": u, "result": r} for u, r in results]

    # Write JSON
    os.makedirs(os.path.dirname(args.out), exist_ok=True)
    with open(args.out, "w", encoding="utf-8") as f:
        json.dump({"summary": counts, "results": data}, f, ensure_ascii=False, indent=2)
    print(f"Wrote JSON results to {args.out}")

    # Also write CSV next to JSON
    csv_path = os.path.splitext(args.out)[0] + ".csv"
    with open(csv_path, "w", encoding="utf-8", newline="") as f:
        w = csv.writer(f)
        w.writerow(["url", "result"])
        for u, r in results:
            w.writerow([u, r])
    print(f"Wrote CSV results to {csv_path}")

    # Print a few anonymous examples
    anon_examples = [u for u, r in results if r == "anonymous"][:10]
    if anon_examples:
        print("Anonymous examples:")
        for u in anon_examples:
            print(f"  {u}")

    return 0


if __name__ == "__main__":
    try:
        rc = asyncio.run(main())
    except KeyboardInterrupt:
        rc = 130
    sys.exit(rc)
