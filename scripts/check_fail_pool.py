#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Standalone concurrent checker for proxies currently in the FAILURE pool.
- Does NOT import or depend on project modules.
- Loads proxies labeled as 'fail' from SQLite (data/app.db by default), or from a user-provided file.
- Probes anonymity (anonymous vs transparent) and latency via HTTP requests through the proxy.
- Classifies results:
  * candidate_main: anonymous AND latency <= latency_threshold_ms
  * candidate_fail: anonymous AND latency > latency_threshold_ms
  * keep_fail_non_anon: explicit transparent (non-anonymous) — unchanged per requirement
  * unknown: detection failed or inconclusive — unchanged
- Writes JSON/CSV reports; and by default writes back to SQLite to move qualifying proxies to the 'main' pool (no capacity limits). Use --dry-run to skip DB writes.

Usage examples:
  python scripts/check_fail_pool.py --db data/app.db  # process all fail-pool proxies and write back to DB
  python scripts/check_fail_pool.py --from-file my_fail_list.json --concurrency 200 --latency-threshold-ms 800
  python scripts/check_fail_pool.py --from-file fail.csv --out-dir ./out

Input file format (when using --from-file):
  - JSON: array of strings (proxy URLs), or object with key "fail" listing URLs
  - CSV/TXT: one proxy URL per line
"""

import argparse
import asyncio
import csv
import json
import os
import re
import sqlite3
import sys
from dataclasses import dataclass
from pathlib import Path
from typing import List, Optional, Tuple, Dict, Any

import httpx

# A few resilient IP endpoints for redundancy
IP_ENDPOINTS = [
    "https://api.ipify.org?format=json",
    "https://ipinfo.io/json",
    "https://ifconfig.me/all.json",
]

TEST_URL = "https://www.google.com"  # general reachability check

SUPPORTED_PROXY_PREFIX = ("http://", "https://", "socks5://")


@dataclass
class ProbeResult:
    proxy: str
    latency_ms: Optional[float]
    anonymity: Optional[str]  # 'anonymous' | 'transparent' | None
    direct_ip: Optional[str]
    proxy_ip: Optional[str]
    error: Optional[str]


def _detect_proxy_table(cur: sqlite3.Cursor) -> Optional[str]:
    tables = cur.execute("SELECT name FROM sqlite_master WHERE type='table'").fetchall()
    table_names = [r[0] for r in tables]
    for t in table_names:
        try:
            cols = [r[1] for r in cur.execute(f"PRAGMA table_info('{t}')").fetchall()]
            if {"proxy_url", "pool"}.issubset(set(cols)):
                return t
        except Exception:
            continue
    return None


def load_fail_proxies_from_db(db_path: Path, limit: int, *, table: Optional[str] = None, pool_label: str = "fail", verbose: bool = False) -> Tuple[List[str], Optional[str]]:
    urls: List[str] = []
    detected_table: Optional[str] = None
    if not db_path.exists():
        return urls, detected_table
    try:
        conn = sqlite3.connect(str(db_path))
        conn.row_factory = sqlite3.Row
        cur = conn.cursor()
        candidate_table = table or _detect_proxy_table(cur)
        if not candidate_table:
            return urls, None
        detected_table = candidate_table
        if verbose:
            try:
                pools = cur.execute(f"SELECT pool, COUNT(*) AS c FROM {candidate_table} GROUP BY pool").fetchall()
                print("[verbose] pool distribution:", [(r[0], r[1]) for r in pools])
            except Exception:
                pass
        if limit and limit > 0:
            q = f"SELECT proxy_url FROM {candidate_table} WHERE pool = ? LIMIT ?"
            rows = cur.execute(q, (pool_label, limit)).fetchall()
        else:
            q = f"SELECT proxy_url FROM {candidate_table} WHERE pool = ?"
            rows = cur.execute(q, (pool_label,)).fetchall()
        urls = [str(r[0]) for r in rows if isinstance(r[0], str)]
    except Exception:
        return [], detected_table
    finally:
        try:
            conn.close()  # type: ignore
        except Exception:
            pass
    return urls, detected_table


def load_fail_proxies_from_file(path: Path, limit: int) -> List[str]:
    if not path.exists():
        return []
    p_lower = path.name.lower()
    try:
        if p_lower.endswith(".json"):
            data = json.loads(path.read_text(encoding="utf-8"))
            if isinstance(data, list):
                urls = [str(x) for x in data if isinstance(x, str)]
            elif isinstance(data, dict):
                if isinstance(data.get("fail"), list):
                    urls = [str(x) for x in data.get("fail") if isinstance(x, str)]
                else:
                    # try common keys
                    urls = []
                    for k in ("proxies", "urls", "data"):
                        if isinstance(data.get(k), list):
                            urls.extend([str(x) for x in data.get(k) if isinstance(x, str)])
                    urls = list(dict.fromkeys(urls))
            else:
                urls = []
            return urls[:limit]
        else:
            # CSV/TXT: one URL per line
            urls: List[str] = []
            with path.open("r", encoding="utf-8", newline="") as f:
                for line in f:
                    s = line.strip()
                    if s:
                        urls.append(s)
            return urls[:limit]
    except Exception:
        return []


async def fetch_json(client: httpx.AsyncClient, url: str, *, timeout: float) -> Optional[Dict[str, Any]]:
    try:
        r = await client.get(url, timeout=timeout)
        r.raise_for_status()
        return r.json()
    except Exception:
        return None


async def detect_ip(client: httpx.AsyncClient, timeout: float) -> Optional[str]:
    for ep in IP_ENDPOINTS:
        data = await fetch_json(client, ep, timeout=timeout)
        if not data:
            continue
        # try common fields
        if isinstance(data.get("ip"), str):
            return data["ip"].strip()
        if isinstance(data.get("ip_addr"), str):
            return data["ip_addr"].strip()
        # ipinfo style: { ip: "x.x.x.x", ... }
        for k, v in data.items():
            if k.lower() in {"ip", "ip_addr", "query"} and isinstance(v, str):
                return v.strip()
    return None


async def measure_latency_through_proxy(proxy: str, timeout: float) -> Optional[float]:
    try:
        async with httpx.AsyncClient(proxies=proxy, timeout=timeout, verify=False) as client:
            r = await client.get(TEST_URL)
            r.raise_for_status()
            # use elapsed for a rough measure
            return float(r.elapsed.total_seconds() * 1000.0)
    except Exception:
        return None


async def check_one(proxy: str, ip_timeout: float, latency_timeout: float) -> ProbeResult:
    # direct ip
    try:
        async with httpx.AsyncClient(timeout=ip_timeout, verify=False) as direct:
            direct_ip = await detect_ip(direct, timeout=ip_timeout)
    except Exception:
        direct_ip = None

    # proxy ip + latency
    proxy_ip: Optional[str] = None
    latency_ms: Optional[float] = None
    anonymity: Optional[str] = None
    error: Optional[str] = None

    try:
        async with httpx.AsyncClient(proxies=proxy, timeout=ip_timeout, verify=False) as via:
            proxy_ip = await detect_ip(via, timeout=ip_timeout)
    except Exception as e:
        error = f"ip_via_proxy_error: {e}"

    if proxy_ip:
        try:
            latency_ms = await measure_latency_through_proxy(proxy, timeout=latency_timeout)
        except Exception as e:
            error = f"latency_error: {e}"

    # classify anonymity
    try:
        if proxy_ip and direct_ip:
            anonymity = "anonymous" if proxy_ip != direct_ip else "transparent"
        else:
            anonymity = None
    except Exception:
        anonymity = None

    return ProbeResult(proxy=proxy, latency_ms=latency_ms, anonymity=anonymity, direct_ip=direct_ip, proxy_ip=proxy_ip, error=error)


def normalize_proxy_url(u: str) -> Optional[str]:
    if not isinstance(u, str):
        return None
    s = u.strip()
    if not s:
        return None
    # ensure supported prefix
    low = s.lower()
    if low.startswith(SUPPORTED_PROXY_PREFIX):
        return s
    # if looks like host:port, prefix http:// by default
    if re.match(r"^[^:/\s]+:\d+$", s):
        return f"http://{s}"
    return None


async def main() -> int:
    parser = argparse.ArgumentParser(description="Check failure-pool proxies concurrently and classify candidates.")
    parser.add_argument("--db", default="data/app.db", help="Path to sqlite db (default: data/app.db)")
    parser.add_argument("--from-file", dest="from_file", default=None, help="Optional file containing fail proxies (json/csv/txt)")
    parser.add_argument("--limit", type=int, default=0, help="Max number of proxies to check (0 or negative means ALL)")
    parser.add_argument("--concurrency", type=int, default=150, help="Max concurrent checks")
    parser.add_argument("--ip-timeout", type=float, default=5.0, help="Timeout for IP detection per request (seconds)")
    parser.add_argument("--latency-timeout", type=float, default=6.0, help="Timeout for latency test request (seconds)")
    parser.add_argument("--latency-threshold-ms", type=int, default=800, help="Main pool latency threshold (ms)")
    parser.add_argument("--out-dir", default=".", help="Output directory (default: current directory)")
    parser.add_argument("--dry-run", action="store_true", help="Do not write back to DB; only report results")
    parser.add_argument("--table", default=None, help="Explicit table name containing proxy_url & pool (auto-detect if omitted)")
    parser.add_argument("--pool", default="fail", help="Pool label to load from DB (default: fail)")
    parser.add_argument("--verbose", action="store_true", help="Verbose logging")
    args = parser.parse_args()

    out_dir = Path(args.out_dir)
    out_dir.mkdir(parents=True, exist_ok=True)

    # load fail proxies
    urls: List[str] = []
    detected_table: Optional[str] = None
    if args.from_file:
        urls = load_fail_proxies_from_file(Path(args.from_file), args.limit)
    if not urls:
        urls, detected_table = load_fail_proxies_from_db(Path(args.db), args.limit, table=args.table, pool_label=args.pool, verbose=args.verbose)

    # normalize and filter
    norm_urls: List[str] = []
    seen = set()
    for u in urls:
        nu = normalize_proxy_url(u)
        if nu and nu not in seen:
            seen.add(nu)
            norm_urls.append(nu)

    if not norm_urls:
        print("No failure-pool proxies found to check.")
        return 0

    sem = asyncio.Semaphore(args.concurrency)
    results: List[ProbeResult] = []

    async def worker(u: str) -> None:
        async with sem:
            res = await check_one(u, args.ip_timeout, args.latency_timeout)
            results.append(res)

    await asyncio.gather(*[worker(u) for u in norm_urls])

    # classify according to requirement
    thr = args.latency_threshold_ms
    candidate_main: List[ProbeResult] = []  # anonymous & fast
    candidate_fail: List[ProbeResult] = []  # anonymous but slow
    keep_fail_non_anon: List[ProbeResult] = []  # explicit transparent
    unknown: List[ProbeResult] = []  # None/inconclusive

    for r in results:
        if r.anonymity is None:
            unknown.append(r)
        elif r.anonymity == "transparent":
            keep_fail_non_anon.append(r)
        elif r.anonymity == "anonymous":
            if r.latency_ms is not None and r.latency_ms <= thr:
                candidate_main.append(r)
            else:
                candidate_fail.append(r)
        else:
            unknown.append(r)

    def to_jsonable(lst: List[ProbeResult]) -> List[Dict[str, Any]]:
        return [
            {
                "proxy": x.proxy,
                "latency_ms": x.latency_ms,
                "anonymity": x.anonymity,
                "direct_ip": x.direct_ip,
                "proxy_ip": x.proxy_ip,
                "error": x.error,
            }
            for x in lst
        ]

    # save JSON
    report = {
        "summary": {
            "total_checked": len(results),
            "candidate_main": len(candidate_main),
            "candidate_fail": len(candidate_fail),
            "keep_fail_non_anon": len(keep_fail_non_anon),
            "unknown": len(unknown),
            "latency_threshold_ms": thr,
            "db_path": str(Path(args.db).resolve()),
            "table": detected_table,
            "write_back": (not args.dry_run),
        },
        "candidate_main": to_jsonable(candidate_main),
        "candidate_fail": to_jsonable(candidate_fail),
        "keep_fail_non_anon": to_jsonable(keep_fail_non_anon),
        "unknown": to_jsonable(unknown),
    }
    json_path = out_dir / "fail_pool_check_results.json"
    json_path.write_text(json.dumps(report, ensure_ascii=False, indent=2), encoding="utf-8")

    # save CSV quick lists
    def write_csv(filename: str, lst: List[ProbeResult]) -> None:
        p = out_dir / filename
        with p.open("w", encoding="utf-8", newline="") as f:
            w = csv.writer(f)
            w.writerow(["proxy", "latency_ms", "anonymity", "direct_ip", "proxy_ip", "error"])
            for x in lst:
                w.writerow([x.proxy, ("%.0f" % x.latency_ms) if isinstance(x.latency_ms, (int, float)) and x.latency_ms is not None else "",
                            x.anonymity or "", x.direct_ip or "", x.proxy_ip or "", x.error or ""])

    write_csv("candidate_main.csv", candidate_main)
    write_csv("candidate_fail.csv", candidate_fail)
    write_csv("keep_fail_non_anon.csv", keep_fail_non_anon)
    write_csv("unknown.csv", unknown)

    # Also emit plain lists for easy import
    def write_list(filename: str, lst: List[ProbeResult]) -> None:
        p = out_dir / filename
        p.write_text("\n".join([x.proxy for x in lst]), encoding="utf-8")

    write_list("candidate_main.txt", candidate_main)
    write_list("candidate_fail.txt", candidate_fail)

    # Write back to DB: move qualifying proxies to 'main' pool (no cap), keep others as-is
    if not args.dry_run and not args.from_file:
        dbp = Path(args.db)
        if dbp.exists() and detected_table:
            try:
                conn = sqlite3.connect(str(dbp))
                cur = conn.cursor()
                # Only update rows that we originally loaded from fail pool
                # Set candidate_main -> pool='main'; candidate_fail -> pool='fail' (idempotent)
                def batched_update(urls: List[str], new_pool: str) -> None:
                    if not urls:
                        return
                    # Use executemany for efficiency
                    cur.executemany(
                        f"UPDATE {detected_table} SET pool = ? WHERE proxy_url = ?",
                        [(new_pool, u) for u in urls]
                    )

                batched_update([x.proxy for x in candidate_main], "main")
                batched_update([x.proxy for x in candidate_fail], args.pool)
                conn.commit()
                report["summary"]["db_updates"] = {
                    "to_main": len(candidate_main),
                    "to_fail": len(candidate_fail),
                }
            except Exception as e:
                report["summary"]["db_update_error"] = str(e)
            finally:
                try:
                    conn.close()  # type: ignore
                except Exception:
                    pass

            # Re-write JSON to reflect DB update outcome
            json_path.write_text(json.dumps(report, ensure_ascii=False, indent=2), encoding="utf-8")

    print("== Fail Pool Check Summary ==")
    print(json.dumps(report["summary"], ensure_ascii=False, indent=2))
    print(f"Reports written to: {out_dir}")
    return 0


if __name__ == "__main__":
    try:
        raise SystemExit(asyncio.run(main()))
    except KeyboardInterrupt:
        print("Interrupted")
        raise SystemExit(1)
