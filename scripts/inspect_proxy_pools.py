#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立检查脚本：统计各个代理池(main/warm/fail)数量、最近检查情况及可能异常线索。
不依赖项目内部模块，优先读取项目根目录 .env，自动适配 SQLite 或 MySQL。

用法:
  # 自动读取 .env 决定数据库
  python scripts/inspect_proxy_pools.py

  # 或强制指定 SQLite DB 文件
  python scripts/inspect_proxy_pools.py /path/to/app.db
"""
import os
import sys
import sqlite3
from pathlib import Path
from typing import Dict, Tuple, Optional


def q1(cur, sql, params=()):
    cur.execute(sql, params)
    row = cur.fetchone()
    return row[0] if row else 0


def qall(cur, sql, params=()):
    cur.execute(sql, params)
    return cur.fetchall()


def load_env(env_path: Path) -> Dict[str, str]:
    env: Dict[str, str] = {}
    try:
        for line in env_path.read_text(encoding="utf-8").splitlines():
            line = line.strip()
            if not line or line.startswith("#"):
                continue
            if "=" in line:
                k, v = line.split("=", 1)
                env[k.strip()] = v.strip().strip('"').strip("'")
    except Exception:
        pass
    return env


def connect_sqlite(db_path: Path) -> Tuple[sqlite3.Connection, sqlite3.Cursor, str]:
    if not db_path.exists():
        raise FileNotFoundError(f"SQLite DB 文件不存在: {db_path.resolve()}")
    conn = sqlite3.connect(str(db_path))
    conn.row_factory = sqlite3.Row
    cur = conn.cursor()
    return conn, cur, f"sqlite:///{db_path.resolve()}"


def connect_mysql(host: str, port: int, user: str, password: str, database: str):
    try:
        import pymysql  # type: ignore
    except Exception as e:
        print("[ERR] 需要 PyMySQL 才能连接 MySQL，请先安装: pip install pymysql")
        raise e
    conn = pymysql.connect(
        host=host,
        port=int(port),
        user=user,
        password=password,
        database=database,
        charset="utf8mb4",
        autocommit=True,
        cursorclass=pymysql.cursors.DictCursor,
    )
    cur = conn.cursor()
    return conn, cur, f"mysql://{user}@{host}:{port}/{database}"


def resolve_connection(argv: list) -> Tuple[object, object, str, str]:
    """
    返回: (conn, cur, dsn, driver) 其中 driver in {"sqlite","mysql"}
    优先顺序:
    1) 若提供 argv[1] 则使用 SQLite 文件
    2) 读取项目根目录 .env 的 DATABASE_TYPE
       - sqlite: data/{SQLITE_DATABASE}（若为空，回退 data/app.db 或 data/default_db）
       - mysql: 使用 MYSQL_* 参数
    """
    if len(argv) > 1:
        path = Path(argv[1])
        conn, cur, dsn = connect_sqlite(path)
        return conn, cur, dsn, "sqlite"

    root = Path(__file__).resolve().parents[1]
    env = load_env(root / ".env")
    db_type = (env.get("DATABASE_TYPE") or "sqlite").lower()
    if db_type == "mysql":
        host = env.get("MYSQL_HOST") or os.getenv("MYSQL_HOST") or "localhost"
        port = int(env.get("MYSQL_PORT") or os.getenv("MYSQL_PORT") or 3306)
        user = env.get("MYSQL_USER") or os.getenv("MYSQL_USER") or "root"
        pwd = env.get("MYSQL_PASSWORD") or os.getenv("MYSQL_PASSWORD") or ""
        db = env.get("MYSQL_DATABASE") or os.getenv("MYSQL_DATABASE") or ""
        if not all([host, port, user, db is not None]):
            print("[WARN] .env 中 MySQL 配置不完整，尝试回退到 SQLite。")
        else:
            conn, cur, dsn = connect_mysql(host, port, user, pwd, db)
            return conn, cur, dsn, "mysql"

    # sqlite 分支
    sqlite_name = env.get("SQLITE_DATABASE") or os.getenv("SQLITE_DATABASE") or "default_db"
    candidates = [Path("data") / sqlite_name, Path("data/app.db"), Path("data/default_db")]
    last_err: Optional[Exception] = None
    for p in candidates:
        try:
            conn, cur, dsn = connect_sqlite(p)
            return conn, cur, dsn, "sqlite"
        except Exception as e:
            last_err = e
            continue
    raise last_err or FileNotFoundError("无法找到可用的 SQLite 数据库文件，请显式传入路径。")


def main():
    # 建立连接
    conn, cur, dsn, driver = resolve_connection(sys.argv)

    # 表存在性检查
    try:
        if driver == "sqlite":
            exists = qall(cur, "SELECT name FROM sqlite_master WHERE type='table' AND name='t_proxies'")
            if not exists:
                print("[ERR] 表 t_proxies 不存在，确认数据库是否初始化/迁移成功 & 路径是否正确。")
                sys.exit(2)
        else:
            cur.execute("SHOW TABLES LIKE 't_proxies'")
            exists = cur.fetchall()
            if not exists:
                print("[ERR] 表 t_proxies 不存在（MySQL）。请确认迁移是否已执行，或 .env 配置的数据库是否正确。")
                sys.exit(2)
    except Exception as e:
        print(f"[ERR] 读取表结构失败: {e}")
        sys.exit(3)

    print(f"== 检查数据库: {dsn} ==")

    total = q1(cur, "SELECT COUNT(*) FROM t_proxies")
    by_pool = qall(cur, "SELECT IFNULL(pool,'(NULL)') AS pool, COUNT(*) AS cnt FROM t_proxies GROUP BY IFNULL(pool,'(NULL)') ORDER BY cnt DESC")
    null_pool = q1(cur, "SELECT COUNT(*) FROM t_proxies WHERE pool IS NULL OR TRIM(pool) = ''")

    warm_cnt = q1(cur, "SELECT COUNT(*) FROM t_proxies WHERE pool='warm'")
    fail_cnt = q1(cur, "SELECT COUNT(*) FROM t_proxies WHERE pool='fail'")
    main_cnt = q1(cur, "SELECT COUNT(*) FROM t_proxies WHERE pool='main'")

    print("\n[总体情况]")
    print(f"- total: {total}")
    print(f"- by pool:")
    for row in by_pool:
        print(f"  * {row['pool']}: {row['cnt']}")
    print(f"- pool 为空(NULL/空白)记录数: {null_pool}")

    failure_gt0 = q1(cur, "SELECT COUNT(*) FROM t_proxies WHERE failure_count > 0")
    transparent_cnt = q1(cur, "SELECT COUNT(*) FROM t_proxies WHERE LOWER(IFNULL(anonymity,''))='transparent'")

    print("\n[失败与匿名性线索]")
    print(f"- failure_count > 0 的记录: {failure_gt0}")
    print(f"- 匿名性=transparent 的记录: {transparent_cnt}")

    # 最近检查分布（依赖 SQLite datetime 函数，SQLAlchemy 默认写入文本时间戳）
    last_1h = q1(cur, "SELECT COUNT(*) FROM t_proxies WHERE last_checked_at >= datetime('now','-1 hour')")
    last_6h = q1(cur, "SELECT COUNT(*) FROM t_proxies WHERE last_checked_at >= datetime('now','-6 hour')")
    last_24h = q1(cur, "SELECT COUNT(*) FROM t_proxies WHERE last_checked_at >= datetime('now','-1 day')")
    never_checked = q1(cur, "SELECT COUNT(*) FROM t_proxies WHERE last_checked_at IS NULL")

    print("\n[最近检查时间分布]")
    print(f"- 最近1小时检查: {last_1h}")
    print(f"- 最近6小时检查: {last_6h}")
    print(f"- 最近24小时检查: {last_24h}")
    print(f"- 从未检查(last_checked_at NULL): {never_checked}")

    # 主池分组
    print("\n[主池(main)维度]")
    print(f"- main 总数: {main_cnt}")
    top_main = qall(cur, """
        SELECT provider, api_group, COUNT(*) AS cnt
        FROM t_proxies
        WHERE pool='main'
        GROUP BY provider, api_group
        ORDER BY cnt DESC
        LIMIT 15
    """)
    for r in top_main:
        print(f"  * {r['provider']} / {r['api_group']}: {r['cnt']}")

    # 热池样本：长时间未检测
    print("\n[热池(warm)线索]")
    print(f"- warm 总数: {warm_cnt}")
    sample_warm_unchecked = qall(cur, """
        SELECT proxy_url, last_checked_at
        FROM t_proxies
        WHERE pool='warm' AND (last_checked_at IS NULL OR last_checked_at < datetime('now','-1 day'))
        LIMIT 10
    """)
    if sample_warm_unchecked:
        print("  * 示例（warm 中长时间未检测的记录）:")
        for r in sample_warm_unchecked:
            print(f"    - {r['proxy_url']} | last_checked_at={r['last_checked_at']}")

    # 失败池样本/线索
    print("\n[失败池(fail)线索]")
    print(f"- fail 总数: {fail_cnt}")
    likely_fail = qall(cur, """
        SELECT proxy_url, anonymity, failure_count, last_connect_ms, last_e2e_ms, last_checked_at
        FROM t_proxies
        WHERE (LOWER(IFNULL(anonymity,''))='transparent' OR failure_count >= 3)
        ORDER BY failure_count DESC
        LIMIT 10
    """)
    if fail_cnt == 0 and likely_fail:
        print("  * 可能应当进入 fail 的样例（但当前不在 fail 池）:")
        for r in likely_fail:
            print(
                f"    - {r['proxy_url']} | anonymity={r['anonymity']} | failures={r['failure_count']} | "
                f"connect_ms={r['last_connect_ms']} | e2e_ms={r['last_e2e_ms']} | last_checked_at={r['last_checked_at']}"
            )

    # 协议分布（简单看是否混入不支持协议，虽然项目层已过滤）
    print("\n[协议分布]")
    proto_stats = qall(cur, """
        SELECT
          CASE
            WHEN LOWER(proxy_url) LIKE 'http://%%' THEN 'http'
            WHEN LOWER(proxy_url) LIKE 'https://%%' THEN 'https'
            WHEN LOWER(proxy_url) LIKE 'socks5://%%' THEN 'socks5'
            ELSE 'other'
          END AS proto,
          COUNT(*) as cnt
        FROM t_proxies
        GROUP BY proto
        ORDER BY cnt DESC
    """)
    for r in proto_stats:
        print(f"  * {r['proto']}: {r['cnt']}")

    try:
        conn.close()
    except Exception:
        pass


if __name__ == "__main__":
    main()
