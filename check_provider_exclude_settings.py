#!/usr/bin/env python3
"""
检查主池服务商的排除区域设置
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.insert(0, '/home/<USER>/应用/轮询')

async def check_provider_exclude_settings():
    """检查主池服务商的排除区域设置"""
    print("=== 检查主池服务商的排除区域设置 ===\n")
    
    try:
        from app.database.connection import database
        from app.database.models import ProviderConfigModel
        from sqlalchemy import select
        
        await database.connect()
        
        # 1. 检查数据库中的服务商配置
        print("📊 步骤1: 检查数据库中的服务商配置")
        
        config_query = select(ProviderConfigModel)
        db_configs = await database.fetch_all(config_query)
        
        if db_configs:
            print("数据库中的服务商配置:")
            for config in db_configs:
                print(f"\n{config.provider_name}:")
                print(f"  启用: {config.enabled}")
                print(f"  基础URL: {config.base_url}")
                print(f"  支持区域: {config.supported_regions}")
                print(f"  排除国家: {config.exclude_countries}")
                print(f"  API组: {config.api_groups}")
        else:
            print("❌ 数据库中没有服务商配置")
        
        # 2. 检查代码中的默认配置
        print(f"\n⚙️  步骤2: 检查代码中的默认配置")
        
        try:
            from extensions.provider.config import get_provider_config
            
            pcfg = get_provider_config()
            configs = pcfg.list_configs()
            
            print("当前生效的服务商配置:")
            for name, cfg in configs.items():
                print(f"\n{name}:")
                print(f"  启用: {cfg.enabled}")
                print(f"  基础URL: {cfg.base_url}")
                print(f"  支持区域: {cfg.supported_regions}")
                print(f"  排除国家: {cfg.exclude_countries}")
                print(f"  API组: {cfg.api_groups}")
        
        except Exception as e:
            print(f"获取服务商配置失败: {e}")
        
        # 3. 分析排除区域设置
        print(f"\n🔍 步骤3: 分析排除区域设置")
        
        if 'configs' in locals():
            print("排除区域分析:")
            
            all_excludes = set()
            for name, cfg in configs.items():
                if cfg.enabled and cfg.exclude_countries:
                    excludes = set(cfg.exclude_countries)
                    all_excludes.update(excludes)
                    print(f"\n{name} 排除的国家:")
                    for country in sorted(excludes):
                        print(f"  - {country}")
            
            print(f"\n所有服务商排除的国家总计: {sorted(all_excludes)}")
            
            # 检查特殊情况
            gemini_excludes = set(configs.get('gemini', type('', (), {'exclude_countries': []})).exclude_countries or [])
            other_excludes = set()
            for name, cfg in configs.items():
                if name != 'gemini' and cfg.enabled and cfg.exclude_countries:
                    other_excludes.update(cfg.exclude_countries)
            
            gemini_only = gemini_excludes - other_excludes
            if gemini_only:
                print(f"\n只有gemini排除的国家: {sorted(gemini_only)}")
            
            common_excludes = gemini_excludes & other_excludes
            if common_excludes:
                print(f"所有服务商都排除的国家: {sorted(common_excludes)}")
        
        # 4. 检查主池中的代理区域分布
        print(f"\n📊 步骤4: 检查主池中的代理区域分布")
        
        from app.database.models import Proxy
        from sqlalchemy import func
        
        region_query = select(
            Proxy.region,
            func.count(Proxy.id).label('count')
        ).where(Proxy.pool == 'main').group_by(Proxy.region)
        
        region_results = await database.fetch_all(region_query)
        
        if region_results:
            print("主池中的代理区域分布:")
            for row in region_results:
                region = row['region'] or 'Unknown'
                count = row['count']
                print(f"  {region}: {count} 个代理")
            
            # 检查是否有被排除的区域
            if 'configs' in locals():
                print(f"\n排除区域检查:")
                for row in region_results:
                    region = row['region']
                    if region:
                        for name, cfg in configs.items():
                            if cfg.enabled and cfg.exclude_countries and region.upper() in [c.upper() for c in cfg.exclude_countries]:
                                print(f"  ⚠️  {region} 被 {name} 排除，但主池中有 {row['count']} 个该区域的代理")
        else:
            print("主池中没有代理")
        
        # 5. 检查各服务商的实际分配
        print(f"\n🔍 步骤5: 检查各服务商的实际分配")
        
        provider_region_query = select(
            Proxy.provider,
            Proxy.region,
            func.count(Proxy.id).label('count')
        ).where(Proxy.pool == 'main').group_by(Proxy.provider, Proxy.region)
        
        provider_region_results = await database.fetch_all(provider_region_query)
        
        if provider_region_results:
            print("各服务商的区域分配:")
            current_provider = None
            for row in provider_region_results:
                provider = row['provider'] or 'NULL'
                region = row['region'] or 'Unknown'
                count = row['count']
                
                if provider != current_provider:
                    current_provider = provider
                    print(f"\n{provider}:")
                
                print(f"  {region}: {count} 个代理")
                
                # 检查是否违反排除规则
                if 'configs' in locals() and provider in configs:
                    cfg = configs[provider]
                    if cfg.exclude_countries and region and region.upper() in [c.upper() for c in cfg.exclude_countries]:
                        print(f"    ❌ 违反排除规则！{provider} 不应该有 {region} 区域的代理")
        
        # 6. 总结和建议
        print(f"\n💡 总结和建议:")
        
        print("当前排除区域设置:")
        if 'configs' in locals():
            for name, cfg in configs.items():
                if cfg.enabled:
                    excludes = cfg.exclude_countries or []
                    print(f"  {name}: {excludes if excludes else '无排除'}")
        
        print(f"\n建议:")
        print("1. 确保主池分配逻辑正确应用排除规则")
        print("2. 定期检查主池中是否有违反排除规则的代理")
        print("3. 考虑在代理选择时再次验证排除规则")
        print("4. 监控各服务商的实际可用代理数量")
        
        # 7. 验证排除逻辑
        print(f"\n🧪 步骤7: 验证排除逻辑")
        
        if 'configs' in locals():
            print("排除逻辑验证:")
            
            # 模拟排除逻辑
            test_regions = ['CN', 'US', 'RU', 'IR', 'KP', 'GB', 'JP']
            
            for region in test_regions:
                print(f"\n区域 {region}:")
                for name, cfg in configs.items():
                    if cfg.enabled:
                        excludes = {c.upper() for c in (cfg.exclude_countries or [])}
                        is_excluded = region.upper() in excludes
                        status = "❌ 排除" if is_excluded else "✅ 允许"
                        print(f"  {name}: {status}")
        
        print(f"\n🎉 检查完成！")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            await database.disconnect()
        except:
            pass

if __name__ == "__main__":
    asyncio.run(check_provider_exclude_settings())
