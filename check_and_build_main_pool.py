#!/usr/bin/env python3
"""
检查代理池状态并手动构建主池
"""

import asyncio
import os
from extensions.proxy.manager import get_proxy_manager
from extensions.provider.config import get_provider_config

async def check_and_build_main_pool():
    """检查代理池状态并构建主池"""
    print("=== 检查代理池状态并构建主池 ===\n")
    
    # 设置环境
    os.environ["GITHUB_TOKEN"] = "*********************************************************************************************"
    
    try:
        mgr = get_proxy_manager()
        
        # 1. 检查当前池状态
        print("📊 步骤1: 检查当前池状态")
        main_snapshot, warm_snapshot, fail_snapshot = mgr.pools.snapshot()
        
        print(f"主池: {sum(len(urls) for partition in main_snapshot.values() for urls in partition.values())} 个代理")
        print(f"热池: {len(warm_snapshot)} 个代理")
        print(f"失败池: {len(fail_snapshot)} 个代理")
        
        # 显示主池详情
        if main_snapshot:
            print("\n主池详情:")
            for provider, partition in main_snapshot.items():
                for api_group, urls in partition.items():
                    print(f"  {provider}/{api_group}: {len(urls)} 个代理")
        else:
            print("⚠️  主池为空！")
        
        # 2. 分析热池中的候选代理
        print(f"\n🔍 步骤2: 分析热池中的主池候选代理")
        
        main_candidates = []
        anonymous_candidates = []
        low_latency_candidates = []
        
        for url, info in warm_snapshot.items():
            lat = info.last_connect_ms if info.last_connect_ms is not None else info.last_latency_ms
            anonymity = (info.anonymity or '').lower()
            
            # 检查延迟条件
            if lat is not None and lat <= 200:
                low_latency_candidates.append((url, lat, anonymity))
                
                # 检查匿名性条件
                if anonymity in ['anonymous', 'elite', 'high_anonymous']:
                    main_candidates.append((url, lat, anonymity))
                elif anonymity:
                    anonymous_candidates.append((url, lat, anonymity))
        
        print(f"延迟≤200ms的代理: {len(low_latency_candidates)} 个")
        print(f"匿名代理: {len(anonymous_candidates)} 个")
        print(f"主池候选代理: {len(main_candidates)} 个")
        
        if main_candidates:
            print("\n主池候选代理（前10个）:")
            for i, (url, lat, anon) in enumerate(main_candidates[:10]):
                print(f"  {i+1}. {url} - {lat}ms - {anon}")
        
        # 3. 检查服务商配置
        print(f"\n⚙️  步骤3: 检查服务商配置")
        
        try:
            pcfg = get_provider_config()
            cfgs = pcfg.list_configs()
            
            provider_api_groups = {}
            for name, cfg in cfgs.items():
                if cfg.enabled and cfg.api_groups:
                    provider_api_groups[name] = cfg.api_groups
                    print(f"  {name}: enabled={cfg.enabled}, api_groups={cfg.api_groups}")
            
            print(f"\n活跃服务商: {list(provider_api_groups.keys())}")
            
        except Exception as e:
            print(f"获取服务商配置失败: {e}")
            # 使用默认配置
            provider_api_groups = {
                "groq": ["chat", "embeddings"],
                "vercel": ["chat", "embeddings", "images"],
                "gemini": ["chat"]
            }
            print(f"使用默认配置: {provider_api_groups}")
        
        # 4. 手动构建主池
        print(f"\n🏗️ 步骤4: 手动构建主池")
        
        if not main_candidates:
            print("❌ 没有符合条件的主池候选代理")
            print("原因可能是:")
            print("1. 代理延迟过高（>200ms）")
            print("2. 代理匿名性检测未完成或不符合要求")
            print("3. 代理分类还在进行中")
            
            # 检查是否有正在分类的代理
            print(f"\n🔍 检查正在分类的代理:")
            classifying_count = 0
            for url, info in warm_snapshot.items():
                if info.last_checked_at is None or info.anonymity is None:
                    classifying_count += 1
            
            print(f"可能正在分类的代理: {classifying_count} 个")
            
            if classifying_count > 0:
                print("建议等待代理分类完成后再次尝试构建主池")
            
        else:
            print(f"开始构建主池，候选代理: {len(main_candidates)} 个")
            
            try:
                await mgr.build_provider_partitions(provider_api_groups)
                print("✅ 主池构建完成")
                
                # 检查构建结果
                new_main_snapshot, _, _ = mgr.pools.snapshot()
                total_main = sum(len(urls) for partition in new_main_snapshot.values() for urls in partition.values())
                
                print(f"\n📊 构建结果:")
                print(f"主池总计: {total_main} 个代理")
                
                if new_main_snapshot:
                    for provider, partition in new_main_snapshot.items():
                        for api_group, urls in partition.items():
                            print(f"  {provider}/{api_group}: {len(urls)} 个代理")
                
                # 保存状态到数据库
                print(f"\n💾 保存状态到数据库...")
                await mgr.save_state_to_db()
                print("✅ 状态已保存到数据库")
                
            except Exception as e:
                print(f"❌ 主池构建失败: {e}")
                import traceback
                traceback.print_exc()
        
        # 5. 建议
        print(f"\n💡 建议:")
        
        if not main_candidates:
            print("1. 等待代理分类和匿名性检测完成")
            print("2. 降低主池延迟阈值（如果需要）")
            print("3. 检查代理源质量")
            print("4. 运行代理刷新任务获取更多代理")
        else:
            print("1. 主池已构建，可以开始使用")
            print("2. 定期运行刷新任务保持代理质量")
            print("3. 监控代理性能和可用性")
        
        # 6. 测试代理选择
        if provider_api_groups and main_candidates:
            print(f"\n🧪 步骤5: 测试代理选择")
            
            for provider in list(provider_api_groups.keys())[:2]:  # 测试前2个服务商
                for api_group in provider_api_groups[provider][:1]:  # 测试第1个API组
                    try:
                        proxy = await mgr.choose_proxy(provider, api_group)
                        if proxy:
                            print(f"✅ {provider}/{api_group}: {proxy}")
                        else:
                            print(f"❌ {provider}/{api_group}: 无可用代理")
                    except Exception as e:
                        print(f"❌ {provider}/{api_group}: 选择失败 - {e}")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    await check_and_build_main_pool()

if __name__ == "__main__":
    asyncio.run(main())
