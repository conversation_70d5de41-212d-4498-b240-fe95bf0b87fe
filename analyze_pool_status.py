#!/usr/bin/env python3
"""
分析代理池状态，找出为什么热池不减少、失败池不增加
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
sys.path.insert(0, '/home/<USER>/应用/轮询')

async def analyze_pool_status():
    """分析代理池状态"""
    print("=== 分析代理池状态问题 ===\n")
    
    try:
        from app.database.connection import database
        from app.database.models import Proxy
        from sqlalchemy import select, func, text
        
        await database.connect()
        
        # 1. 检查各池的详细状态
        print("📊 步骤1: 检查各池的详细状态")
        
        pool_query = select(
            Proxy.pool,
            func.count(Proxy.id).label('count')
        ).group_by(Proxy.pool)
        
        pool_results = await database.fetch_all(pool_query)
        
        print("各池代理数量:")
        total_proxies = 0
        for row in pool_results:
            pool = row['pool'] or 'NULL'
            count = row['count']
            total_proxies += count
            print(f"  {pool}: {count:,} 个代理")
        
        print(f"总计: {total_proxies:,} 个代理")
        
        # 2. 检查最近的分类活动
        print(f"\n🔍 步骤2: 检查最近的分类活动")
        
        # 检查最近1小时内更新的代理
        one_hour_ago = datetime.now() - timedelta(hours=1)
        
        recent_query = select(
            Proxy.pool,
            func.count(Proxy.id).label('count')
        ).where(
            Proxy.last_checked_at >= one_hour_ago
        ).group_by(Proxy.pool)
        
        recent_results = await database.fetch_all(recent_query)
        
        print("最近1小时内更新的代理:")
        recent_total = 0
        for row in recent_results:
            pool = row['pool'] or 'NULL'
            count = row['count']
            recent_total += count
            print(f"  {pool}: {count:,} 个代理")
        
        print(f"最近1小时总更新: {recent_total:,} 个代理")
        
        # 3. 检查失败次数分布
        print(f"\n📊 步骤3: 检查失败次数分布")
        
        failure_query = text("""
            SELECT 
                CASE 
                    WHEN failure_count = 0 THEN '0次失败'
                    WHEN failure_count = 1 THEN '1次失败'
                    WHEN failure_count BETWEEN 2 AND 5 THEN '2-5次失败'
                    WHEN failure_count BETWEEN 6 AND 10 THEN '6-10次失败'
                    WHEN failure_count > 10 THEN '>10次失败'
                    ELSE 'NULL'
                END as failure_range,
                pool,
                COUNT(*) as count
            FROM t_proxies 
            GROUP BY failure_range, pool
            ORDER BY failure_count
        """)
        
        failure_results = await database.fetch_all(failure_query)
        
        print("失败次数分布:")
        for row in failure_results:
            failure_range = row['failure_range']
            pool = row['pool'] or 'NULL'
            count = row['count']
            print(f"  {failure_range} - {pool}池: {count:,} 个代理")
        
        # 4. 检查延迟分布
        print(f"\n⏱️ 步骤4: 检查延迟分布")
        
        latency_query = text("""
            SELECT 
                CASE 
                    WHEN last_connect_ms IS NULL THEN 'NULL'
                    WHEN last_connect_ms <= 100 THEN '≤100ms'
                    WHEN last_connect_ms <= 200 THEN '101-200ms'
                    WHEN last_connect_ms <= 500 THEN '201-500ms'
                    WHEN last_connect_ms <= 1000 THEN '501-1000ms'
                    WHEN last_connect_ms <= 5000 THEN '1-5秒'
                    ELSE '>5秒'
                END as latency_range,
                pool,
                COUNT(*) as count
            FROM t_proxies 
            GROUP BY latency_range, pool
            ORDER BY 
                CASE 
                    WHEN last_connect_ms IS NULL THEN 999999
                    ELSE last_connect_ms
                END
        """)
        
        latency_results = await database.fetch_all(latency_query)
        
        print("延迟分布:")
        for row in latency_results:
            latency_range = row['latency_range']
            pool = row['pool'] or 'NULL'
            count = row['count']
            print(f"  {latency_range} - {pool}池: {count:,} 个代理")
        
        # 5. 检查匿名性分布
        print(f"\n🔍 步骤5: 检查匿名性分布")
        
        anonymity_query = select(
            Proxy.anonymity,
            Proxy.pool,
            func.count(Proxy.id).label('count')
        ).group_by(Proxy.anonymity, Proxy.pool)
        
        anonymity_results = await database.fetch_all(anonymity_query)
        
        print("匿名性分布:")
        for row in anonymity_results:
            anonymity = row['anonymity'] or 'NULL'
            pool = row['pool'] or 'NULL'
            count = row['count']
            print(f"  {anonymity} - {pool}池: {count:,} 个代理")
        
        # 6. 检查最近失败的代理
        print(f"\n❌ 步骤6: 检查最近失败的代理")
        
        # 查找最近检查但仍在热池的高失败次数代理
        failed_in_warm_query = select(Proxy).where(
            (Proxy.pool == 'warm') &
            (Proxy.failure_count >= 3) &
            (Proxy.last_checked_at >= one_hour_ago)
        ).limit(10)
        
        failed_in_warm = await database.fetch_all(failed_in_warm_query)
        
        if failed_in_warm:
            print("热池中的高失败次数代理（应该被移到失败池）:")
            for proxy in failed_in_warm:
                print(f"  {proxy.proxy_url} - 失败{proxy.failure_count}次 - 延迟{proxy.last_connect_ms}ms")
        else:
            print("热池中没有高失败次数的代理")
        
        # 7. 检查内存池状态（如果可能）
        print(f"\n💾 步骤7: 检查内存池状态")
        
        try:
            from extensions.proxy.manager import get_proxy_manager
            
            mgr = get_proxy_manager()
            main_snapshot, warm_snapshot, fail_snapshot = mgr.pools.snapshot()
            
            print("内存池状态:")
            print(f"  主池: {sum(len(urls) for partition in main_snapshot.values() for urls in partition.values())} 个代理")
            print(f"  热池: {len(warm_snapshot)} 个代理")
            print(f"  失败池: {len(fail_snapshot)} 个代理")
            
            # 检查内存和数据库的差异
            db_warm_count = next((row['count'] for row in pool_results if row['pool'] == 'warm'), 0)
            db_fail_count = next((row['count'] for row in pool_results if row['pool'] == 'fail'), 0)
            
            print(f"\n内存vs数据库对比:")
            print(f"  热池: 内存{len(warm_snapshot)} vs 数据库{db_warm_count}")
            print(f"  失败池: 内存{len(fail_snapshot)} vs 数据库{db_fail_count}")
            
            if len(warm_snapshot) != db_warm_count or len(fail_snapshot) != db_fail_count:
                print("⚠️  内存和数据库状态不一致！")
                print("这可能是问题的根源：内存中的变化没有保存到数据库")
            
        except Exception as e:
            print(f"无法检查内存池状态: {e}")
        
        # 8. 分析问题原因
        print(f"\n🔍 步骤8: 问题原因分析")
        
        # 统计应该被移到失败池的代理
        should_be_failed_query = select(func.count(Proxy.id)).where(
            (Proxy.pool == 'warm') &
            (Proxy.failure_count >= 3)
        )
        should_be_failed_count = await database.fetch_val(should_be_failed_query)
        
        # 统计NULL延迟的代理
        null_latency_query = select(func.count(Proxy.id)).where(
            (Proxy.pool == 'warm') &
            (Proxy.last_connect_ms == None)
        )
        null_latency_count = await database.fetch_val(null_latency_query)
        
        print("问题分析:")
        print(f"- 热池中失败次数≥3的代理: {should_be_failed_count:,} 个（应该移到失败池）")
        print(f"- 热池中没有延迟数据的代理: {null_latency_count:,} 个（可能还在分类中）")
        
        if should_be_failed_count > 0:
            print("❌ 问题1: 失败的代理没有被正确移到失败池")
        
        if null_latency_count > 30000:  # 如果大部分代理都没有延迟数据
            print("❌ 问题2: 代理分类进度缓慢，大量代理还未完成检测")
        
        # 9. 建议解决方案
        print(f"\n💡 建议解决方案:")
        
        if should_be_failed_count > 0:
            print("1. 手动清理失败的代理:")
            print("   - 将失败次数≥3的热池代理移到失败池")
            print("   - 强制同步内存池状态到数据库")
        
        if null_latency_count > 30000:
            print("2. 加速代理分类:")
            print("   - 增加并发度")
            print("   - 降低超时时间")
            print("   - 跳过明显无效的代理")
        
        print("3. 检查代理分类逻辑:")
        print("   - 确保失败的代理被正确标记")
        print("   - 确保池状态变化被保存到数据库")
        
        print(f"\n🔧 手动操作:")
        print("# 清理失败代理")
        print("UPDATE t_proxies SET pool='fail' WHERE pool='warm' AND failure_count >= 3;")
        print("\n# 强制保存池状态")
        print("curl -X POST http://localhost:8001/ext/web/proxies/resync_from_db")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            await database.disconnect()
        except:
            pass

if __name__ == "__main__":
    asyncio.run(analyze_pool_status())
