<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;b37cc73c-f83a-4e2e-9d9b-3b0fde343d8b&quot;,&quot;conversations&quot;:{&quot;b37cc73c-f83a-4e2e-9d9b-3b0fde343d8b&quot;:{&quot;id&quot;:&quot;b37cc73c-f83a-4e2e-9d9b-3b0fde343d8b&quot;,&quot;createdAtIso&quot;:&quot;2025-08-10T13:48:58.584Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-08-10T13:48:58.584Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasAgentOnboarded&quot;:true,&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;2ef7df59-0f9e-4756-8cf7-3c3dd0e15997&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>