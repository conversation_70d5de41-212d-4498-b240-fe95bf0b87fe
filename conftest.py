import pytest
import json

@pytest.fixture(scope="module")
def failover_proxy_config(tmpdir_factory):
    """Creates a temporary proxy config file for failover testing."""
    config = {
        "proxies": [
            "http://127.0.0.1:1080", # A good proxy (will be mocked)
            "http://127.0.0.1:1081", # A bad proxy (will be mocked)
        ],
        "block_direct_connections": True
    }
    config_path = tmpdir_factory.mktemp("data").join("proxy_config_failover.json")
    with open(config_path, "w") as f:
        json.dump(config, f)
    
    yield str(config_path)

@pytest.fixture(scope="module")
def proxy_only_config(tmpdir_factory):
    """Creates a temporary proxy config file for proxy-only enforcement testing."""
    config = {
        "proxies": [
            "http://127.0.0.1:1081", # A bad proxy (will be mocked)
        ],
        "block_direct_connections": True
    }
    config_path = tmpdir_factory.mktemp("data").join("proxy_config_proxy_only.json")
    with open(config_path, "w") as f:
        json.dump(config, f)
    
    yield str(config_path)
