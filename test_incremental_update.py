#!/usr/bin/env python3
"""
测试增量更新和智能分类功能
"""

import asyncio
from app.database.connection import database
from extensions.proxy.manager import get_proxy_manager
from extensions.proxy.ip_pool import ProxyInfo

async def test_incremental_update():
    """测试增量更新功能"""
    await database.connect()
    
    try:
        mgr = get_proxy_manager()
        
        print("=== 测试增量更新功能 ===")
        
        # 1. 获取初始状态
        print("\n1. 获取初始状态...")
        main_snapshot, warm_urls, fail_urls = mgr.pools.snapshot()
        initial_warm_count = len(warm_urls)
        initial_fail_count = len(fail_urls)
        print(f"   初始热池: {initial_warm_count} 个")
        print(f"   初始失败池: {initial_fail_count} 个")
        
        # 2. 测试添加新代理（模拟增量更新）
        print("\n2. 测试添加新代理...")
        test_proxies = [
            "http://test1.example.com:8080",
            "http://test2.example.com:8080", 
            "http://existing.proxy.com:8080"  # 假设这个已存在
        ]
        
        # 先添加一个已存在的代理到热池
        existing_proxy = ProxyInfo(proxy_url="http://existing.proxy.com:8080")
        mgr.pools._warm["http://existing.proxy.com:8080"] = existing_proxy
        
        # 测试增量添加
        new_proxies = [ProxyInfo(proxy_url=url) for url in test_proxies]
        await mgr._add_new_proxies_incrementally(new_proxies)
        
        # 检查结果
        main_snapshot, warm_urls, fail_urls = mgr.pools.snapshot()
        new_warm_count = len(warm_urls)
        print(f"   添加后热池: {new_warm_count} 个")
        print(f"   增加了: {new_warm_count - initial_warm_count} 个代理")
        
        # 3. 测试智能分类
        print("\n3. 测试智能分类...")
        
        # 测试延迟合格的代理（应该保持在热池）
        test_proxy = mgr.pools._warm.get("http://test1.example.com:8080")
        if test_proxy:
            print("   测试延迟合格的代理...")
            await mgr.pools.classify(test_proxy, 150)  # 150ms，合格
            if "http://test1.example.com:8080" in mgr.pools._warm:
                print("   ✅ 延迟合格的代理保持在热池")
            else:
                print("   ❌ 延迟合格的代理被错误移动")
        
        # 测试延迟过高的代理（应该移动到失败池）
        test_proxy2 = mgr.pools._warm.get("http://test2.example.com:8080")
        if test_proxy2:
            print("   测试延迟过高的代理...")
            await mgr.pools.classify(test_proxy2, 2000)  # 2000ms，过高
            if "http://test2.example.com:8080" in mgr.pools._fail:
                print("   ✅ 延迟过高的代理移动到失败池")
            else:
                print("   ❌ 延迟过高的代理未正确移动")
        
        # 4. 测试智能失败记录
        print("\n4. 测试智能失败记录...")
        
        # 添加一个测试代理
        test_fail_proxy = ProxyInfo(proxy_url="http://test.fail.com:8080")
        mgr.pools._warm["http://test.fail.com:8080"] = test_fail_proxy
        
        # 记录多次失败
        for i in range(2):  # 记录2次失败，应该还在热池
            await mgr.pools.record_failure("http://test.fail.com:8080")
            print(f"   失败次数: {test_fail_proxy.failure_count}")
        
        if "http://test.fail.com:8080" in mgr.pools._warm:
            print("   ✅ 失败次数未达到阈值，代理保持在热池")
        
        # 再记录一次失败，应该移动到失败池
        await mgr.pools.record_failure("http://test.fail.com:8080")
        print(f"   失败次数: {test_fail_proxy.failure_count}")
        
        if "http://test.fail.com:8080" in mgr.pools._fail:
            print("   ✅ 失败次数达到阈值，代理移动到失败池")
        else:
            print("   ❌ 失败次数达到阈值，但代理未移动")
        
        # 5. 测试代理恢复
        print("\n5. 测试代理恢复...")
        
        # 将失败的代理重新分类为合格
        if "http://test.fail.com:8080" in mgr.pools._fail:
            failed_proxy = mgr.pools._fail["http://test.fail.com:8080"]
            await mgr.pools.classify(failed_proxy, 100)  # 100ms，很好的延迟
            
            if "http://test.fail.com:8080" in mgr.pools._warm:
                print("   ✅ 失败的代理恢复到热池")
                print(f"   失败计数减少到: {failed_proxy.failure_count}")
            else:
                print("   ❌ 失败的代理未能恢复")
        
        # 6. 最终状态
        print("\n6. 最终状态...")
        main_snapshot, warm_urls, fail_urls = mgr.pools.snapshot()
        final_warm_count = len(warm_urls)
        final_fail_count = len(fail_urls)
        print(f"   最终热池: {final_warm_count} 个")
        print(f"   最终失败池: {final_fail_count} 个")
        
        print("\n=== 测试完成 ===")
        
    finally:
        await database.disconnect()

async def test_startup_initialization():
    """测试启动时的初始化"""
    await database.connect()
    
    try:
        print("\n=== 测试启动初始化 ===")
        
        mgr = get_proxy_manager()
        
        # 重置并测试初始化
        print("1. 重置代理池...")
        mgr.reset_pools()
        
        print("2. 从数据库初始化...")
        await mgr.initialize(crawl=False)
        
        main_snapshot, warm_urls, fail_urls = mgr.pools.snapshot()
        print(f"   从数据库加载: 主池={len([url for partition in main_snapshot.values() for urls in partition.values() for url in urls])}, 热池={len(warm_urls)}, 失败池={len(fail_urls)}")
        
        print("3. 测试增量抓取...")
        await mgr._crawl_and_merge_new_proxies()
        
        main_snapshot, warm_urls, fail_urls = mgr.pools.snapshot()
        print(f"   增量抓取后: 主池={len([url for partition in main_snapshot.values() for urls in partition.values() for url in urls])}, 热池={len(warm_urls)}, 失败池={len(fail_urls)}")
        
        print("=== 启动初始化测试完成 ===")
        
    finally:
        await database.disconnect()

async def main():
    """主测试函数"""
    print("开始测试增量更新和智能分类功能...")
    
    await test_incremental_update()
    await test_startup_initialization()
    
    print("\n所有测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
