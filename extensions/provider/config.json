{"provider_order": ["groq", "gemini", "vercel"], "vercel": {"enabled": true, "base_url": "https://ai-gateway.vercel.sh/v1", "supported_regions": ["US", "EU"], "exclude_countries": null, "api_groups": ["chat", "embeddings", "images"]}, "groq": {"enabled": true, "base_url": "https://api.groq.com/openai/v1", "supported_regions": ["US", "EU"], "exclude_countries": null, "api_groups": ["chat", "embeddings"]}, "gemini": {"enabled": true, "base_url": "https://generativelanguage.googleapis.com/v1beta", "supported_regions": ["US", "EU"], "exclude_countries": ["CU", "IR", "KP", "SY", "RU", "BY", "SD", "CN"], "api_groups": ["chat"]}}