from __future__ import annotations
from typing import Dict, List
from .types import Provider


class ProviderRegistry:
    def __init__(self) -> None:
        self._providers: Dict[str, Provider] = {}

    def register(self, provider: Provider) -> None:
        self._providers[provider.name] = provider

    def get(self, name: str) -> Provider:
        return self._providers[name]

    def list(self) -> List[Provider]:
        return list(self._providers.values())


global_registry = ProviderRegistry()
