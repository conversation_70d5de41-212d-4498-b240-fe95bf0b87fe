from __future__ import annotations
from typing import Optional


def is_ip_banned(provider: str, status_code: int, body_text: Optional[str]) -> bool:
    """Heuristic detection of IP-ban signals from provider response.
    Conservative: only triggers on strong indicators.
    """
    if not body_text:
        return False
    t = (body_text or "").lower()
    # Strong indicators
    keywords = [
        "ip banned",
        "ip ban",
        "your ip has been blocked",
        "access denied from your ip",
        "forbidden by firewall",
        "ip address is blocked",
        "proxy not allowed",
        "proxies are not allowed",
    ]
    if any(k in t for k in keywords):
        return True
    # Status-aided weak indicators
    if status_code in (401, 403) and ("your ip" in t and ("blocked" in t or "banned" in t or "forbidden" in t)):
        return True
    if status_code == 429 and ("ip" in t and ("block" in t or "ban" in t or "rate limit" in t)):
        return True
    return False

