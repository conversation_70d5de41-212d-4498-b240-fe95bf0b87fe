"""
Response converter for different providers to OpenAI format
"""
import json
import uuid
import time
from typing import Dict, Any, Optional, AsyncGenerator

def convert_gemini_to_openai(gemini_response: Dict[str, Any], model: str) -> Dict[str, Any]:
    """Convert Gemini response to OpenAI format"""
    
    # Generate OpenAI-compatible response structure
    openai_response = {
        "id": f"chatcmpl-{uuid.uuid4().hex[:8]}",
        "object": "chat.completion",
        "created": int(time.time()),
        "model": model,
        "choices": [],
        "usage": {
            "prompt_tokens": 0,
            "completion_tokens": 0,
            "total_tokens": 0
        }
    }
    
    # Extract candidates
    candidates = gemini_response.get("candidates", [])
    if not candidates:
        return openai_response
    
    # Convert first candidate to OpenAI choice
    first_candidate = candidates[0]
    content_parts = first_candidate.get("content", {}).get("parts", [])
    
    # Extract text content
    text_content = ""
    for part in content_parts:
        if "text" in part:
            text_content += part["text"]
    
    # Create choice
    choice = {
        "index": 0,
        "message": {
            "role": "assistant",
            "content": text_content
        },
        "logprobs": None,
        "finish_reason": _convert_finish_reason(first_candidate.get("finishReason"))
    }
    
    openai_response["choices"].append(choice)
    
    # Convert usage metadata
    usage_metadata = gemini_response.get("usageMetadata", {})
    if usage_metadata:
        openai_response["usage"] = {
            "prompt_tokens": usage_metadata.get("promptTokenCount", 0),
            "completion_tokens": usage_metadata.get("candidatesTokenCount", 0),
            "total_tokens": usage_metadata.get("totalTokenCount", 0)
        }
    
    return openai_response

def _convert_finish_reason(gemini_reason: Optional[str]) -> str:
    """Convert Gemini finish reason to OpenAI format"""
    if not gemini_reason:
        return "stop"
    
    reason_map = {
        "STOP": "stop",
        "MAX_TOKENS": "length",
        "SAFETY": "content_filter",
        "RECITATION": "content_filter",
        "OTHER": "stop"
    }
    
    return reason_map.get(gemini_reason, "stop")

async def convert_gemini_stream_to_openai(
    gemini_stream: AsyncGenerator[str, None], 
    model: str
) -> AsyncGenerator[str, None]:
    """Convert Gemini streaming response to OpenAI format"""
    
    chat_id = f"chatcmpl-{uuid.uuid4().hex[:8]}"
    created_time = int(time.time())
    first_chunk_sent = False
    
    async for line in gemini_stream:
        # Only process data: lines
        if line.startswith("data: "):
            data_part = line[6:]  # Remove "data: " prefix
            
            try:
                gemini_chunk = json.loads(data_part)
                
                # Send first chunk with role if not sent yet
                if not first_chunk_sent:
                    first_chunk = {
                        "id": chat_id,
                        "object": "chat.completion.chunk",
                        "created": created_time,
                        "model": model,
                        "choices": [{
                            "index": 0,
                            "delta": {"role": "assistant", "content": ""},
                            "logprobs": None,
                            "finish_reason": None
                        }]
                    }
                    yield f"data: {json.dumps(first_chunk)}"
                    first_chunk_sent = True
                
                # Extract content from Gemini chunk
                candidates = gemini_chunk.get("candidates", [])
                if candidates:
                    candidate = candidates[0]
                    content_parts = candidate.get("content", {}).get("parts", [])
                    
                    # Extract text content
                    text_content = ""
                    for part in content_parts:
                        if "text" in part:
                            text_content += part["text"]
                    
                    # Get finish reason
                    finish_reason = _convert_finish_reason(candidate.get("finishReason"))
                    
                    # Create OpenAI chunk
                    openai_chunk = {
                        "id": chat_id,
                        "object": "chat.completion.chunk",
                        "created": created_time,
                        "model": model,
                        "choices": [{
                            "index": 0,
                            "delta": {"content": text_content} if text_content else {},
                            "logprobs": None,
                            "finish_reason": finish_reason
                        }]
                    }
                    
                    yield f"data: {json.dumps(openai_chunk)}"
                    
                    # If this is the final chunk, break
                    if finish_reason and finish_reason != "null":
                        break
                    
            except json.JSONDecodeError as e:
                # Skip invalid JSON but log for debugging
                print(f"JSON decode error: {e}, line: {line}")
                continue
    
    # Final [DONE] marker
    yield "data: [DONE]"