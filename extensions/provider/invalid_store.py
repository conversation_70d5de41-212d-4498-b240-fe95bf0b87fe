from __future__ import annotations
import json
from pathlib import Path
from typing import Dict


class InvalidKeyStore:
    """
    记录每个服务商下各 key 的“疑似失效”失败次数（命中失效规则时+1）。
    达到阈值（默认3）后，供前端提示“确认删除/忽略重置”。
    """
    def __init__(self, storage_path: str | None = None, threshold: int = 3) -> None:
        self._path = Path(storage_path) if storage_path else Path(__file__).with_name("invalid_keys.json")
        self._data: Dict[str, Dict[str, int]] = {}
        self._threshold = threshold
        self._load()

    def _load(self) -> None:
        try:
            if self._path.exists():
                self._data = json.loads(self._path.read_text(encoding="utf-8")) or {}
        except Exception:
            self._data = {}

    def _save(self) -> None:
        try:
            self._path.write_text(json.dumps(self._data, ensure_ascii=False, indent=2), encoding="utf-8")
        except Exception:
            pass

    def incr(self, provider: str, key: str) -> int:
        prov_map = self._data.setdefault(provider, {})
        prov_map[key] = int(prov_map.get(key, 0)) + 1
        self._save()
        return prov_map[key]

    def success(self, provider: str, key: str) -> None:
        """成功一次就将该 key 的失效计数归 0（防止偶发误报）。"""
        prov_map = self._data.setdefault(provider, {})
        if key in prov_map and prov_map[key] != 0:
            prov_map[key] = 0
            self._save()

    def reset(self, provider: str, key: str) -> None:
        prov_map = self._data.setdefault(provider, {})
        if key in prov_map:
            prov_map[key] = 0
            self._save()

    def remove(self, provider: str, key: str) -> None:
        prov_map = self._data.setdefault(provider, {})
        if key in prov_map:
            del prov_map[key]
            self._save()

    def list(self, provider: str | None = None) -> Dict[str, Dict[str, int]]:
        if provider:
            return {provider: self._data.get(provider, {})}
        return self._data

    def threshold(self) -> int:
        return self._threshold


_global_invalid_store: InvalidKeyStore | None = None


def get_invalid_store() -> InvalidKeyStore:
    global _global_invalid_store
    if _global_invalid_store is None:
        _global_invalid_store = InvalidKeyStore()
    return _global_invalid_store

