from __future__ import annotations
from typing import Dict, List

# 可配置的“密钥失效”信号：按服务商定制
# 仅当出现 401/403 这类权限相关状态码，并且返回体包含关键字时，判定为“疑似密钥失效”
RULES: Dict[str, Dict[str, List[str]]] = {
    # 通用 OpenAI 兼容
    "openai": {
        "status": [401, 403],
        "substr": [
            "invalid api key",
            "incorrect api key",
            "unauthorized",
            "invalid_token",
            "api key is not valid",
        ],
    },
    # groq/vercel 大多兼容 openai 语义
    "groq": {"status": [401, 403], "substr": ["invalid", "unauthorized", "api key"]},
    "vercel": {"status": [401, 403], "substr": ["invalid", "unauthorized", "api key"]},
    # gemini（示例关键词，可按你现有逻辑补充/修改）
    "gemini": {"status": [401, 403], "substr": ["permission_denied", "api key not valid", "invalid api key"]},
}

DEFAULT_RULE = {"status": [401, 403], "substr": ["invalid", "unauthorized", "api key"]}


def is_invalid_key(provider: str, status_code: int, body_text: str) -> bool:
    name = (provider or "").lower()
    rule = RULES.get(name, DEFAULT_RULE)
    if status_code not in rule.get("status", []):
        return False
    body = (body_text or "").lower()
    for kw in rule.get("substr", []):
        if kw and kw.lower() in body:
            return True
    return False

