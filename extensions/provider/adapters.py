from __future__ import annotations
from typing import Any, Dict, Tuple

# 适配器：将 OpenAI 兼容的请求转换为各服务商特定格式（当该服务商非 OpenAI 兼容时）
# 目前支持：gemini（示例）。groq/vercel 使用 OpenAI 兼容，无需转换。


def is_openai_compatible(provider_name: str) -> bool:
    name = (provider_name or "").lower()
    return name in {"openai", "groq", "vercel", "openai-compatible", "openrouter"}


def transform_request(provider_name: str, path: str, payload: Dict[str, Any]) -> Tuple[str, Dict[str, Any]]:
    name = (provider_name or "").lower()
    if name == "gemini":
        return _to_gemini(path, payload)
    # 默认 passthrough（OpenAI 兼容）
    return path, payload


def _to_gemini(path: str, payload: Dict[str, Any]) -> Tuple[str, Dict[str, Any]]:
    """
    将 OpenAI Chat Completions 请求转换为 Gemini generateContent/streamGenerateContent。
    仅处理典型的 /chat/completions；其他路径默认透传。
    """
    norm_path = path.lstrip()
    if not norm_path.startswith("/"):
        norm_path = "/" + norm_path
    if "/chat/completions" not in norm_path:
        return path, payload

    # 期望 payload 包含 model, messages, stream 等字段
    model = payload.get("model", "gemini-1.5-pro")
    messages = payload.get("messages") or []
    is_stream = bool(payload.pop("stream", False))  # 不把 stream 传给 Gemini 后端

    # 复用已有的 OpenAI -> Gemini 消息转换器
    try:
        from app.handler.message_converter import OpenAIMessageConverter
    except Exception:
        # 回退：简单拼接文本
        text = "\n\n".join([m.get("content", "") if isinstance(m.get("content"), str) else (m.get("content", [{}])[0].get("text", "") if isinstance(m.get("content"), list) else "") for m in messages])
        gemini_body = {
            "contents": [{"role": "user", "parts": [{"text": text}]}]
        }
        suffix = "streamGenerateContent" if is_stream else "generateContent"
        return f"/v1beta/models/{model}:{suffix}", gemini_body

    conv = OpenAIMessageConverter()
    converted, system_instruction = conv.convert(messages)
    gemini_body: Dict[str, Any] = {
        "contents": converted
    }
    if system_instruction:
        gemini_body["systemInstruction"] = system_instruction

    # Gemini endpoint 路径（流式/非流式区别）
    suffix = "streamGenerateContent" if is_stream else "generateContent"
    return f"/v1beta/models/{model}:{suffix}", gemini_body

