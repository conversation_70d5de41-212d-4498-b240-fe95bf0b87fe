from __future__ import annotations
from dataclasses import dataclass
from typing import Dict, List, Optional


@dataclass(frozen=True)
class Provider:
    name: str  # e.g. 'vercel', 'groq', 'gemini', 'openai-compatible'
    base_url: str
    supported_regions: Optional[List[str]] = None  # ISO country codes or provider-specific
    # map api route group to weight; can be used for weighted round-robin later
    api_weights: Optional[Dict[str, int]] = None


@dataclass
class ProviderCallContext:
    provider: Provider
    api_group: str  # e.g. 'chat', 'embeddings', 'images', 'gemini:generateContent'
    model: Optional[str] = None
    api_key: Optional[str] = None
