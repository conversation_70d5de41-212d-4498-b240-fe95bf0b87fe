from __future__ import annotations
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional
import datetime
import asyncio

from app.database.connection import database
from app.database.models import ProviderConfigModel
from sqlalchemy import select, insert, update, delete


@dataclass
class ProviderConfig:
    enabled: bool = True
    base_url: str = ""
    base_urls: Optional[List[str]] = None  # 已废弃：不再支持多个 API Base 轮询
    supported_regions: Optional[List[str]] = None
    exclude_countries: Optional[List[str]] = None  # 不支持国家（排除法）
    api_groups: Optional[List[str]] = None



class ProviderConfigStore:
    def __init__(self) -> None:
        self._configs: Dict[str, ProviderConfig] = {}

    async def _load(self) -> None:
        self._configs.clear()
        # Load from database
        query = select(ProviderConfigModel)
        db_configs = await database.fetch_all(query)

        for db_cfg in db_configs:
            self._configs[db_cfg.provider_name] = ProviderConfig(
                enabled=db_cfg.enabled,
                base_url=db_cfg.base_url,
                supported_regions=db_cfg.supported_regions,
                exclude_countries=db_cfg.exclude_countries,
                api_groups=db_cfg.api_groups,
            )

        # Ensure defaults for known providers if missing
        # 只设置排除区域，不设置允许区域（排除就是反向允许）
        default_excludes = ["CU","IR","KP","SY","RU","BY","SD"]  # 古巴、伊朗、朝鲜、叙利亚、俄罗斯、白俄罗斯、苏丹
        self._configs.setdefault("vercel", ProviderConfig(enabled=True, base_url="https://ai-gateway.vercel.sh/v1", api_groups=["chat","embeddings","images"], exclude_countries=default_excludes))
        self._configs.setdefault("groq", ProviderConfig(enabled=True, base_url="https://api.groq.com/openai/v1", api_groups=["chat","embeddings"], exclude_countries=default_excludes))
        self._configs.setdefault("gemini", ProviderConfig(enabled=True, base_url="https://generativelanguage.googleapis.com", api_groups=["chat"], exclude_countries=default_excludes + ["CN"]))

    async def _save(self) -> None:
        # Save to database
        # Clear existing configs first
        await database.execute(delete(ProviderConfigModel))

        configs_to_insert = []
        for name, cfg in self._configs.items():
            configs_to_insert.append({
                "provider_name": name,
                "enabled": cfg.enabled,
                "base_url": cfg.base_url,
                "supported_regions": cfg.supported_regions,
                "exclude_countries": cfg.exclude_countries,
                "api_groups": cfg.api_groups,
            })
        
        if configs_to_insert:
            await database.execute(insert(ProviderConfigModel).values(configs_to_insert))

    async def initialize(self) -> None:
        await self._load()

    def list_configs(self) -> Dict[str, ProviderConfig]:
        return dict(self._configs)

    def get(self, name: str) -> ProviderConfig:
        return self._configs[name]

    def get_provider_api_groups(self) -> Dict[str, List[str]]:
        """Return mapping provider -> api_groups with sensible defaults.
        Used by scheduler/manager when building partitions.
        """
        out: Dict[str, List[str]] = {}
        for name, cfg in self._configs.items():
            groups = list(cfg.api_groups or ["chat", "embeddings", "images"])
            if name == "groq" and not cfg.api_groups:
                groups = ["chat", "embeddings"]
            out[name] = groups
        return out

    def ensure(self, name: str) -> ProviderConfig:
        if name not in self._configs:
            self._configs[name] = ProviderConfig(enabled=False)
        return self._configs[name]

    async def update(self, name: str, *, enabled: Optional[bool] = None, base_url: Optional[str] = None, supported_regions: Optional[List[str]] = None, api_groups: Optional[List[str]] = None) -> ProviderConfig:
        cfg = self.ensure(name)
        if enabled is not None:
            cfg.enabled = bool(enabled)
        if base_url is not None:
            cfg.base_url = base_url
        if supported_regions is not None:
            cfg.supported_regions = list(supported_regions)
        if api_groups is not None:
            cfg.api_groups = list(api_groups)
        await self._save()
        return cfg


_global_cfg: Optional[ProviderConfigStore] = None
_initialized: bool = False


def get_provider_config() -> ProviderConfigStore:
    global _global_cfg
    if _global_cfg is None:
        _global_cfg = ProviderConfigStore()
    return _global_cfg


async def get_provider_config_async() -> ProviderConfigStore:
    """异步获取服务商配置，确保初始化完成"""
    global _global_cfg, _initialized
    if _global_cfg is None:
        _global_cfg = ProviderConfigStore()

    if not _initialized:
        await _global_cfg.initialize()
        _initialized = True

    return _global_cfg
