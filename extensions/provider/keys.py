from __future__ import annotations
import os
import json
import asyncio
from itertools import cycle
from typing import Dict, Iterator, List, Optional
import datetime
from pathlib import Path

from app.database.connection import database
from app.database.models import <PERSON><PERSON><PERSON>ey
from sqlalchemy import select, insert, update, delete


class ProviderKeyCycler:
    def __init__(self, keys: List[str]) -> None:
        self._keys = [k.strip() for k in keys if k and k.strip()]
        self._it: Optional[Iterator[str]] = cycle(self._keys) if self._keys else None

    def next(self) -> Optional[str]:
        if not self._it:
            return None
        return next(self._it)


class ProviderKeys:
    def __init__(self) -> None:
        self._cyclers: Dict[str, ProviderKeyCycler] = {}

    # 从文件加载密钥配置
    def _load_from_file(self) -> None:
        self._cyclers.clear()
        try:
            import json
            from pathlib import Path
            keys_file = Path(__file__).parent / "keys.json"
            if keys_file.exists():
                data = json.loads(keys_file.read_text(encoding="utf-8"))
                for provider, keys in data.items():
                    if isinstance(keys, list) and keys:
                        self._cyclers[provider] = ProviderKeyCycler(keys)
        except Exception:
            pass

    async def _load(self) -> None:
        # DB 优先：持久化的设置应在重启后生效
        self._cyclers.clear()
        db_map: Dict[str, List[str]] = {}
        try:
            query = select(ApiKey)
            rows = await database.fetch_all(query)
            for r in rows:
                try:
                    db_map.setdefault(r.provider_name, []).append(r.api_key)
                except Exception:
                    pass
        except Exception:
            db_map = {}
        # 先用 DB 构建内存
        for provider, keys in db_map.items():
            self._cyclers[provider] = ProviderKeyCycler(keys)
        # 环境变量仅在 DB 无对应 provider 时作为种子
        env_keys = {
            "vercel": os.getenv("VERCEL_API_KEYS", "").split(","),
            "groq": os.getenv("GROQ_API_KEYS", "").split(","),
            "gemini": os.getenv("GEMINI_API_KEYS", "").split(","),
        }
        for provider, keys in env_keys.items():
            if provider not in self._cyclers and any(k.strip() for k in keys):
                self._cyclers[provider] = ProviderKeyCycler(keys)

        # 文件配置作为补充：为数据库和环境变量中缺失的服务商提供配置
        try:
            import json
            from pathlib import Path
            keys_file = Path(__file__).parent / "keys.json"
            if keys_file.exists():
                file_data = json.loads(keys_file.read_text(encoding="utf-8"))
                for provider, keys in file_data.items():
                    if isinstance(keys, list) and keys and provider not in self._cyclers:
                        self._cyclers[provider] = ProviderKeyCycler(keys)
        except Exception:
            pass

    async def set_keys(self, provider: str, keys: List[str], persist: bool = True) -> None:
        # Update in-memory cycler
        self._cyclers[provider] = ProviderKeyCycler(keys)

        # 仅持久化到 DB（若未被环境变量覆盖）
        if persist:
            env_var_name = f"{provider.upper()}_API_KEYS"
            if not os.getenv(env_var_name):
                await database.execute(delete(ApiKey).where(ApiKey.provider_name == provider))
                keys_to_insert = []
                for key in keys:
                    if key and key.strip():
                        keys_to_insert.append({
                            "provider_name": provider,
                            "api_key": key,
                            "status": "active",
                            "created_at": datetime.datetime.now(),
                            "updated_at": datetime.datetime.now(),
                        })
                if keys_to_insert:
                    await database.execute(insert(ApiKey).values(keys_to_insert))

    def list_providers(self) -> Dict[str, int]:
        return {name: len(cycler._keys) for name, cycler in self._cyclers.items()}

    def get_keys(self, provider: str) -> List[str]:
        cycler = self._cyclers.get(provider)
        return list(cycler._keys) if cycler else []

    async def reload(self) -> None:
        await self._load()

    def next_key(self, provider: str) -> Optional[str]:
        cycler = self._cyclers.get(provider)
        return cycler.next() if cycler else None


_global_keys: Optional[ProviderKeys] = None


def get_provider_keys() -> ProviderKeys:
    global _global_keys
    if _global_keys is None:
        _global_keys = ProviderKeys()
        # 启动时加载：事件循环已运行则异步调度加载；未运行则同步加载
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                asyncio.create_task(_global_keys._load())
            else:
                loop.run_until_complete(_global_keys._load())
        except Exception:
            pass
    return _global_keys
