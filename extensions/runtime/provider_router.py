from __future__ import annotations
from typing import Any, Dict, List, Optional

from app.service.client.api_client import OpenaiApiClient

from ..provider.types import Provider, ProviderCallContext
from ..provider.registry import global_registry
from ..provider.config import get_provider_config


class ProviderRoundRobin:
    def __init__(self, providers: List[Provider]) -> None:
        self.providers = providers
        self._next = 0

    def next(self) -> Provider:
        if not self.providers:
            raise RuntimeError("No providers configured")
        p = self.providers[self._next % len(self.providers)]
        self._next += 1
        return p


class ProviderOrchestrator:
    def __init__(self) -> None:
        self._rr: Optional[ProviderRoundRobin] = None

    def configure(self) -> None:
        cfg = get_provider_config()
        enabled_set = {name for name, pc in cfg.list_configs().items() if pc.enabled}
        providers = [p for p in global_registry.list() if p.name in enabled_set]
        self._rr = ProviderRoundRobin(providers)

    async def call_openai_compatible(
        self,
        ctx: ProviderCallContext,
        payload: Dict[str, Any],
        stream: bool = False,
    ) -> Any:
        assert self._rr is not None, "Orchestrator not configured"

        attempts = 0
        max_attempts = max(1, len(self._rr.providers))
        last_error: Optional[Exception] = None

        while attempts < max_attempts:
            provider = self._rr.next()
            attempts += 1
            try:
                client = OpenaiApiClient(provider.base_url)
                if stream:
                    return await client.stream_generate_content(payload, ctx.api_key, provider=provider.name, api_group=ctx.api_group)
                else:
                    return await client.generate_content(payload, api_key=ctx.api_key, provider=provider.name, api_group=ctx.api_group)
            except Exception as e:
                last_error = e
                continue

        if last_error:
            raise last_error
        raise RuntimeError("Provider orchestration failed without error")


# Global orchestrator
_orchestrator: Optional[ProviderOrchestrator] = None


def get_orchestrator() -> ProviderOrchestrator:
    global _orchestrator
    if _orchestrator is None:
        _orchestrator = ProviderOrchestrator()
    return _orchestrator
