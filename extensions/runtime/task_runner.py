from __future__ import annotations
import asyncio
import time
import uuid
from typing import Any, Callable, Dict, Optional


class TaskInfo:
    __slots__ = ("id", "name", "status", "started_at", "ended_at", "error", "result")

    def __init__(self, name: str) -> None:
        self.id: str = uuid.uuid4().hex
        self.name: str = name
        self.status: str = "running"  # running|done|error
        self.started_at: float = time.time()
        self.ended_at: float = 0.0
        self.error: Optional[str] = None
        self.result: Optional[Any] = None


class TaskRunner:
    def __init__(self) -> None:
        self._tasks: Dict[str, TaskInfo] = {}
        self._lock = asyncio.Lock()

    async def _wrap(self, info: TaskInfo, coro: Callable[[], Any]) -> None:
        try:
            res = await coro()
            info.result = res
            info.status = "done"
        except Exception as e:  # noqa: BLE001
            info.error = str(e)
            info.status = "error"
        finally:
            info.ended_at = time.time()

    async def run_background(self, name: str, coro: Callable[[], Any]) -> str:
        info = TaskInfo(name)
        async with self._lock:
            self._tasks[info.id] = info
        async def runner():
            await self._wrap(info, coro)
        asyncio.create_task(runner())
        return info.id

    async def get(self, task_id: str) -> Optional[Dict[str, Any]]:
        async with self._lock:
            t = self._tasks.get(task_id)
            if not t:
                return None
            return {
                "id": t.id,
                "name": t.name,
                "status": t.status,
                "started_at": t.started_at,
                "ended_at": t.ended_at,
                "error": t.error,
                "result": t.result,
            }


_runner: Optional[TaskRunner] = None

def get_task_runner() -> TaskRunner:
    global _runner
    if _runner is None:
        _runner = TaskRunner()
    return _runner
