from __future__ import annotations
from typing import Any, As<PERSON><PERSON>enerator, Dict, List
import httpx
from datetime import datetime

from ..provider.registry import global_registry
from ..provider.types import Provider


def _resolve_path(api_type: str) -> str:
    if api_type == "chat":
        return "/chat/completions"
    if api_type == "embeddings":
        return "/embeddings"
    if api_type == "images":
        return "/images/generations"
    return "/chat/completions"


_rr_cursor: int = 0
# 基于用户 API 的轮询，仅在“同一服务商内”使用（例如多个 base_urls 轮转）。不跨服务商混合。
_user_api_rr: dict[str, int] = {}



import json
from pathlib import Path

def _candidate_providers(payload: Dict[str, Any]) -> List[str]:
    # 按全局配置的固定顺序轮询；支持 groq / gemini / vercel
    try:
        config_path = Path(__file__).parent.parent / "provider" / "config.json"
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        provider_order = config_data.get("provider_order", ["groq", "gemini", "vercel"])
    except (IOError, json.JSONDecodeError):
        provider_order = ["groq", "gemini", "vercel"]  # 默认顺序
    return provider_order


def _is_model_not_found_error(err: Exception) -> bool:
    msg = str(err).lower()
    return ("model" in msg and "not found" in msg) or "404" in msg or "model_not_found" in msg


def _should_try_next_provider(err: Exception) -> bool:
    """决定是否切换到下一个服务商。
    以下情况都会尝试下一个：
    - 模型不存在/404
    - 鉴权/访问受限：401/403、'access denied'
    - 速率限制：429
    - 缺少该 provider 的 API Key
    - 常见网络错误提示
    """
    msg = str(err).lower()
    if _is_model_not_found_error(err):
        return True
    retry_clues = [
        "status code 401", "status code 403", "status code 429",
        "access denied", "forbidden", "unauthorized",
        "no api key configured", "rate limit", "timeout", "timed out",
        "connection", "network", "temporarily unavailable"
    ]
    return any(k in msg for k in retry_clues)


def _pick_provider_name(payload: Dict[str, Any]) -> str:
    # 已不再用于排序；仅保留向后兼容
    return "vercel"


async def _call_original_openai_generate_content(client, payload: Dict[str, Any], api_key: str, provider: str, api_group: str) -> Dict[str, Any]:
    """直接调用OpenAI API，避免monkey patch递归"""
    proxy_to_use = await client._get_proxy(provider, api_group)

    headers = {"Authorization": f"Bearer {api_key}"}
    from app.config.config import settings
    if settings.CUSTOM_HEADERS:
        headers.update(settings.CUSTOM_HEADERS)

    try:
        httpx_client = client._httpx_client
        if proxy_to_use:
            httpx_client.proxies = {"all://": proxy_to_use}
        else:
            httpx_client.proxies = {}

        url = f"{client.base_url}/chat/completions"
        start_time = datetime.now()
        response = await httpx_client.post(url, json=payload, headers=headers)
        end_time = datetime.now()
        response.raise_for_status()
        return response.json()
    except (httpx.RequestError, httpx.HTTPStatusError) as e:
        if proxy_to_use:
            await client._report_failure(proxy_to_use, provider)
        raise


async def _call_original_openai_stream_generate_content(client, payload: Dict[str, Any], api_key: str, provider: str, api_group: str) -> AsyncGenerator[str, None]:
    """直接调用OpenAI Stream API，避免monkey patch递归"""
    proxy_to_use = await client._get_proxy(provider, api_group)

    headers = {"Authorization": f"Bearer {api_key}"}
    from app.config.config import settings
    if settings.CUSTOM_HEADERS:
        headers.update(settings.CUSTOM_HEADERS)

    try:
        httpx_client = client._httpx_client
        if proxy_to_use:
            httpx_client.proxies = {"all://": proxy_to_use}
        else:
            httpx_client.proxies = {}

        url = f"{client.base_url}/chat/completions"
        async with httpx_client.stream("POST", url, json=payload, headers=headers) as response:
            response.raise_for_status()
            async for line in response.aiter_lines():
                yield line
    except (httpx.RequestError, httpx.HTTPStatusError) as e:
        if proxy_to_use:
            await client._report_failure(proxy_to_use, provider)
        raise


def _get_provider_by_name(name: str) -> Provider:
    try:
        return global_registry.get(name)
    except Exception:
        # 动态从配置创建并注册（使用正确的base_url）
        from ..provider.config import get_provider_config
        try:
            pc = get_provider_config().ensure(name)
            base = (pc.base_url or "").strip()
            if not base:
                # 如果配置中没有base_url，使用默认值
                defaults = {
                    "groq": "https://api.groq.com/openai/v1",
                    "gemini": "https://generativelanguage.googleapis.com/v1beta",
                    "vercel": "https://ai-gateway.vercel.sh/v1"
                }
                base = defaults.get(name, "https://ai-gateway.vercel.sh")
        except Exception:
            # 配置加载失败，使用硬编码默认值
            defaults = {
                "groq": "https://api.groq.com/openai/v1",
                "gemini": "https://generativelanguage.googleapis.com",
                "vercel": "https://ai-gateway.vercel.sh/v1"
            }
            base = defaults.get(name, "https://ai-gateway.vercel.sh")

        prov = Provider(name=name, base_url=base)
        global_registry.register(prov)
        return prov


async def _rr_call_json(api_type: str, payload: Dict[str, Any], api_key: str) -> Dict[str, Any]:
    # 依序尝试 groq → gemini → vercel；遇到模型不存在/鉴权/网络等错误时切换
    last_err: Exception | None = None
    for name in _candidate_providers(payload):
        provider = _get_provider_by_name(name)
        try:
            # 获取provider专用的API key
            from ..provider.keys import get_provider_keys
            provider_keys = get_provider_keys()
            provider_api_key = provider_keys.next_key(name)

            if not provider_api_key:
                # 如果没有provider专用key，跳过这个provider
                continue

            # 根据provider类型选择合适的客户端
            if name == "gemini":
                from app.service.client.api_client import GeminiApiClient
                client = GeminiApiClient(provider.base_url)
                # 允许 "google/" 前缀但在请求时去掉
                model = payload.get("model")
                if isinstance(model, str) and model.startswith("google/"):
                    model = model.split("/", 1)[1]
                # 直接调用父类方法避免递归
                return await GeminiApiClient.generate_content(client, payload, model=model, api_key=provider_api_key, provider=provider.name, api_group="chat")
            else:
                # 其他provider使用OpenAI兼容客户端，直接调用原始实现避免递归
                from app.service.client.api_client import OpenaiApiClient
                client = OpenaiApiClient(provider.base_url)
                # 使用未被monkey patch的原始实现
                return await _call_original_openai_generate_content(client, payload, provider_api_key, provider.name, "chat")
        except Exception as e:
            last_err = e
            if _should_try_next_provider(e):
                continue
            # 其他不可恢复错误，直接抛出
            raise
    if last_err:
        raise last_err
    raise RuntimeError("No providers available for request")

async def _rr_call_stream(api_type: str, payload: Dict[str, Any], api_key: str) -> AsyncGenerator[str, None]:
    # 依序尝试 groq → gemini → vercel；仅当建链即失败（模型不存在/鉴权/网络等）时切换
    last_err: Exception | None = None
    for name in _candidate_providers(payload):
        provider = _get_provider_by_name(name)
        try:
            # 获取provider专用的API key
            from ..provider.keys import get_provider_keys
            provider_keys = get_provider_keys()
            provider_api_key = provider_keys.next_key(name)

            if not provider_api_key:
                # 如果没有provider专用key，跳过这个provider
                continue

            # 根据provider类型选择合适的客户端
            if name == "gemini":
                from app.service.client.api_client import GeminiApiClient
                client = GeminiApiClient(provider.base_url)
                # 允许 "google/" 前缀但在请求时去掉
                model = payload.get("model")
                if isinstance(model, str) and model.startswith("google/"):
                    model = model.split("/", 1)[1]
                async for line in GeminiApiClient.stream_generate_content(client, payload, model=model, api_key=provider_api_key, provider=provider.name, api_group="chat"):
                    yield line
            else:
                # 其他provider使用OpenAI兼容客户端，使用原始实现避免递归
                from app.service.client.api_client import OpenaiApiClient
                client = OpenaiApiClient(provider.base_url)
                async for line in _call_original_openai_stream_generate_content(client, payload, provider_api_key, provider.name, "chat"):
                    yield line
            return  # 完整流结束
        except Exception as e:
            last_err = e
            if _should_try_next_provider(e):
                continue
            raise
    if last_err:
        raise last_err
    raise RuntimeError("No providers available for streaming request")

def apply_monkey_patches() -> None:
     # Import here to avoid circular imports
     from app.service.client.api_client import OpenaiApiClient

     # Patch OpenaiApiClient methods to route via provider RR
     async def generate_content(self, payload: Dict[str, Any], api_key: str, provider: str, api_group: str) -> Dict[str, Any]:
         return await _rr_call_json("chat", payload, api_key)

     async def stream_generate_content(self, payload: Dict[str, Any], api_key: str, provider: str, api_group: str):
         async for line in _rr_call_stream("chat", payload, api_key):
             yield line

     async def create_embeddings(self, input: str, model: str, api_key: str) -> Dict[str, Any]:
         payload = {"input": input, "model": model}
         return await _rr_call_json("embeddings", payload, api_key)

     async def generate_images(self, payload: Dict[str, Any], api_key: str) -> Dict[str, Any]:
         return await _rr_call_json("images", payload, api_key)

     OpenaiApiClient.generate_content = generate_content  # type: ignore
     OpenaiApiClient.stream_generate_content = stream_generate_content  # type: ignore
     OpenaiApiClient.create_embeddings = create_embeddings  # type: ignore
     OpenaiApiClient.generate_images = generate_images  # type: ignore
