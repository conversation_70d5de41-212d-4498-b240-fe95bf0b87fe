from __future__ import annotations


from ..provider.registry import global_registry
from ..provider.types import Provider
from ..provider.config import get_provider_config
from .provider_router import get_orchestrator


async def initialize_extensions() -> None:
    # 1) Register providers from configuration
    cfg = get_provider_config()
    for name in ("vercel", "groq", "gemini"):
        pc = cfg.ensure(name)
        # 优先使用配置中心的 base_url；若为空才使用内置默认值
        defaults = {
            "vercel": "https://ai-gateway.vercel.sh/v1",
            "groq": "https://api.groq.com/openai/v1",
            "gemini": "https://generativelanguage.googleapis.com/v1beta",
        }
        base_url = pc.base_url or defaults.get(name, "https://ai-gateway.vercel.sh")
        global_registry.register(Provider(name=name, base_url=base_url, supported_regions=pc.supported_regions))


    # 2) Init proxy manager: crawl, classify, build partitions
    # This is now handled by the background bootstrap in application.py
    # proxy_mgr = get_proxy_manager()
    # # 启动即爬取（默认开启），并结合本地缓存进行去重合并
    # await proxy_mgr.initialize(crawl=True)
    # # First pass: classify warm pool
    # await proxy_mgr.detect_and_classify_all(sample_limit=200, include_main=False, max_concurrency=100)
    # # Second pass: include current main bindings to refresh latency
    # await proxy_mgr.detect_and_classify_all(sample_limit=200, include_main=True, max_concurrency=100)

    # # 2b) Build partitions using configured api groups only
    # provider_api_groups = {
    #     "vercel": (cfg.get("vercel").api_groups or ["chat","embeddings","images"]),
    #     "groq": (cfg.get("groq").api_groups or ["chat","embeddings"]),
    # }
    # await proxy_mgr.build_provider_partitions(provider_api_groups)

    # 3) Configure orchestrator
    get_orchestrator().configure()

    # 4) Enable monkey patch to route OpenAI-compatible calls via provider RR
    try:
        from .monkey_patch import apply_monkey_patches
        apply_monkey_patches()
    except Exception as e:
        # Non-fatal: if patch fails, normal routing still works
        pass
