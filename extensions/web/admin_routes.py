from __future__ import annotations
from pathlib import Path

from fastapi import APIRouter, Request
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jin<PERSON>2Templates

from app.core.security import verify_auth_token
from extensions.provider.keys import get_provider_keys
from extensions.provider.config import get_provider_config



router = APIRouter(prefix="/ext/admin", tags=["ext-admin-ui"])

TEMPLATES_DIR = Path(__file__).resolve().parents[2] / "app" / "templates"
templates = Jinja2Templates(directory=str(TEMPLATES_DIR))


@router.get("/dashboard", response_class=HTMLResponse)
async def providers_dashboard(request: Request):
    auth_token = request.cookies.get("auth_token")
    if not auth_token or not verify_auth_token(auth_token):
        return RedirectResponse(url="/", status_code=302)
    context = {"request": request}
    return templates.TemplateResponse("ext/providers_dashboard.html", context)


@router.get("/providers", response_class=HTMLResponse)
async def providers_admin(request: Request):
    auth_token = request.cookies.get("auth_token")
    if not auth_token or not verify_auth_token(auth_token):
        return RedirectResponse(url="/", status_code=302)
    keys = get_provider_keys()
    cfg = get_provider_config()
    context = {
        "request": request,
        "providers": keys.list_providers(),
        "configs": cfg.list_configs(),
    }
    return templates.TemplateResponse("ext/providers.html", context)


@router.get("/providers/{provider}", response_class=HTMLResponse)
async def provider_detail(provider: str, request: Request):
    auth_token = request.cookies.get("auth_token")
    if not auth_token or not verify_auth_token(auth_token):
        return RedirectResponse(url="/", status_code=302)
    keys = get_provider_keys()
    cfg = get_provider_config()
    context = {
        "request": request,
        "provider": provider,
        "keys": keys.get_keys(provider),
        "config": cfg.ensure(provider),
    }
    return templates.TemplateResponse("ext/provider_detail.html", context)



