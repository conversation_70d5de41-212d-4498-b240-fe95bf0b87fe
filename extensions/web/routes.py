from __future__ import annotations
from typing import Dict, List, Optional
import asyncio
from datetime import datetime

from fastapi import APIRouter, HTTPException

from ..provider.keys import get_provider_keys
from ..provider.config import get_provider_config

from ..runtime.task_runner import get_task_runner
from app.log.logger import get_application_logger

logger = get_application_logger()

router = APIRouter(prefix="/ext", tags=["extensions"])





@router.get("/providers")
def list_providers() -> Dict[str, int]:
    keys = get_provider_keys()
    return keys.list_providers()

@router.get("/providers/configs")
def get_provider_configs() -> Dict[str, Dict[str, object]]:
    cfg = get_provider_config()
    out: Dict[str, Dict[str, object]] = {}
    for name, pc in cfg.list_configs().items():
        out[name] = {
            "enabled": pc.enabled,
            "base_url": pc.base_url,
            "supported_regions": pc.supported_regions,
            "api_groups": pc.api_groups,
        }
    return out


@router.get("/providers/{provider}/keys")
def get_provider_key_list(provider: str) -> List[str]:
    keys = get_provider_keys()
    return keys.get_keys(provider)


@router.post("/providers/{provider}/keys")
async def set_provider_keys(provider: str, body: Dict[str, List[str]]) -> Dict[str, int]:
    keys = get_provider_keys()
    items = body.get("keys", []) if isinstance(body, dict) else []
    if not isinstance(items, list):
        raise HTTPException(status_code=400, detail="invalid payload: keys must be list[str]")
    await keys.set_keys(provider, items, persist=True)
    return keys.list_providers()


@router.put("/providers/{provider}/config")
async def update_provider_config(provider: str, body: Dict[str, object]) -> Dict[str, object]:
    cfg = get_provider_config()
    enabled = body.get("enabled")
    base_url = body.get("base_url")
    supported_regions = body.get("supported_regions")
    api_groups = body.get("api_groups")
    updated = await cfg.update(
        provider,
        enabled=bool(enabled) if enabled is not None else None,
        base_url=str(base_url) if base_url is not None else None,
        supported_regions=list(supported_regions) if isinstance(supported_regions, list) else None,
        api_groups=list(api_groups) if isinstance(api_groups, list) else None,
    )
    # Return new snapshot for UI refresh
    return {
        "provider": provider,
        "config": {
            "enabled": updated.enabled,
            "base_url": updated.base_url,
            "supported_regions": updated.supported_regions,
            "api_groups": updated.api_groups,
        },
    }





@router.get("/user_api")
async def get_user_api():
    try:
        from ..user.api_store import get_user_api_store
        return {"user_apis": get_user_api_store().list()}
    except Exception:
        return {"user_apis": []}


@router.put("/user_api")
async def set_user_api(body: Dict[str, object]):
    apis: list[str] = []
    if isinstance(body, dict):
        v = body.get("user_apis")
        if isinstance(v, list):
            apis = [str(x).strip() for x in v if isinstance(x, str) and x.strip()]
        else:
            v1 = body.get("user_api")
            if isinstance(v1, str) and v1.strip():
                apis = [v1.strip()]
    try:
        from ..user.api_store import get_user_api_store
        await get_user_api_store().set_all(apis, persist=True)
        return {"ok": True}
    except Exception as e:
        # 返回更详细的错误信息，便于排查
        raise HTTPException(status_code=500, detail=f"persist user_api failed: {e}")


@router.get("/providers/invalid_keys")
def list_invalid_keys(provider: str | None = None) -> Dict[str, Dict[str, int]]:
    from ..provider.invalid_store import get_invalid_store
    store = get_invalid_store()
    return store.list(provider)


@router.get("/providers/{provider}/models")
async def provider_models(provider: str) -> Dict[str, object]:
    from ..provider.config import get_provider_config
    from ..provider.keys import get_provider_keys
    cfg = get_provider_config().list_configs().get(provider)
    if not cfg or not cfg.base_url:
        raise HTTPException(status_code=400, detail=f"provider {provider} base_url not configured")
    keys = get_provider_keys()
    api_key = keys.next_key(provider)
    if not api_key:
        raise HTTPException(status_code=400, detail=f"no api key configured for provider {provider}")
    base = cfg.base_url.rstrip('/')
    # 确保 /v1 前缀
    if not base.endswith('/v1'):
        base = base + '/v1'
    url = f"{base}/models"
    import httpx
    timeout = httpx.Timeout(30.0, read=30.0)
    async with httpx.AsyncClient(timeout=timeout) as client:
        resp = await client.get(url, headers={"Authorization": f"Bearer {api_key}"})
        if resp.status_code != 200:
            raise HTTPException(status_code=resp.status_code, detail=resp.text)
        try:
            data = resp.json()
        except Exception:
            data = {"raw": resp.text}
        return {"provider": provider, "endpoint": url, "result": data}

@router.post("/providers/invalid_keys/reset")
def reset_invalid_key(body: Dict[str, str]):
    from ..provider.invalid_store import get_invalid_store
    provider = body.get("provider", "")
    key = body.get("key", "")
    if not provider or not key:
        raise HTTPException(status_code=400, detail="provider and key required")
    store = get_invalid_store()
    store.reset(provider, key)
    return {"ok": True}

@router.post("/providers/invalid_keys/delete")
def delete_invalid_key(body: Dict[str, str]):
    from ..provider.invalid_store import get_invalid_store
    from ..provider.keys import get_provider_keys
    provider = body.get("provider", "")
    key = body.get("key", "")
    if not provider or not key:
        raise HTTPException(status_code=400, detail="provider and key required")
    # 1) 从 invalid store 移除
    store = get_invalid_store()
    store.remove(provider, key)
    # 2) 从 keys.json 移除并持久化
    keys = get_provider_keys()
    current = keys.get_keys(provider)
    if key in current:
        new_list = [k for k in current if k != key]
        keys.set_keys(provider, new_list, persist=True)
    return {"ok": True}


@router.get("/tasks/{task_id}")
async def get_task_status(task_id: str) -> Dict[str, object]:
    runner = get_task_runner()
    info = await runner.get(task_id)
    if not info:
        raise HTTPException(status_code=404, detail="task not found")
    return info
