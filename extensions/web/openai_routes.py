from __future__ import annotations
import json
from typing import Any, As<PERSON><PERSON><PERSON><PERSON>, Dict

from fastapi import APIRouter, Request
from fastapi.responses import StreamingResponse, JSONResponse

from ..runtime.monkey_patch import _rr_call_json, _rr_call_stream

router = APIRouter(prefix="/ext/openai/v1", tags=["ext-openai-compat"])

@router.get("/test")
async def test_endpoint():
    return {"message": "Test endpoint reached!"}

@router.get("/models")
async def list_models(request: Request) -> Any:
    from ..provider.keys import get_provider_keys
    import httpx

    providers = get_provider_keys().list_providers()
    all_models = []
    async with httpx.AsyncClient() as client:
        for provider_name in providers.keys():
            try:
                url = f"http://127.0.0.1:8001/ext/providers/{provider_name}/models"
                response = await client.get(url)
                if response.status_code == 200:
                    data = response.json()
                    if "result" in data and "data" in data["result"]:
                        all_models.extend(data["result"]["data"])
            except Exception:
                pass
    return {"data": all_models, "object": "list"}

@router.post("/chat/completions")
async def chat_completions(request: Request) -> Any:
    from app.log.logger import get_main_logger
    from app.handler.response_validator import ChatResponseValidator
    
    logger = get_main_logger()
    
    try:
        payload: Dict[str, Any] = await request.json()
        stream = bool(payload.get("stream"))
        # 从请求体中移除本地 api_key，避免上游服务商报错（如 groq: 'property "api_key" is unsupported'）
        payload.pop("api_key", None)
        
        # Log request details for debugging
        model = payload.get("model", "unknown")
        logger.info(f"Chat completion request: model={model}, stream={stream}")
        logger.debug(f"Request payload keys: {list(payload.keys())}")

        if stream:
            async def streamer() -> AsyncGenerator[bytes, None]:
                try:
                    chunk_count = 0
                    # 始终使用 KeyManager 里的 provider key，不透传用户传入的本地 api_key（例如 "1"）
                    async for line in _rr_call_stream("chat", payload, None):
                        chunk_count += 1
                        # Proper SSE format: each data line should end with \n\n (like original Gemini project)
                        if line.startswith("data:"):
                            yield (line + "\n\n").encode("utf-8")
                        elif line.strip():  # Other non-empty lines
                            yield (line + "\n").encode("utf-8")
                        # Skip empty lines as they are handled by the provider layer
                    logger.debug(f"Stream completed successfully, {chunk_count} chunks sent")
                except Exception as e:
                    logger.error(f"Stream error: {str(e)}")
                    # Send error as SSE event
                    error_data = {
                        "error": {
                            "message": f"Stream failed: {str(e)}",
                            "type": "stream_error"
                        }
                    }
                    error_line = f"data: {json.dumps(error_data)}\n\n"
                    yield error_line.encode("utf-8")
                    
            return StreamingResponse(streamer(), media_type="text/event-stream")
        else:
            data = await _rr_call_json("chat", payload, None)
            
            # Additional validation for non-streaming responses
            if isinstance(data, dict):
                is_valid, error_msg = ChatResponseValidator.validate_response(data, "unknown")
                if not is_valid:
                    logger.error(f"Response validation failed: {error_msg}")
                    return JSONResponse(
                        content=ChatResponseValidator.create_error_response(error_msg, "unknown"),
                        status_code=500
                    )
                else:
                    content = ChatResponseValidator.extract_content(data)
                    logger.info(f"Chat completion successful, content length: {len(content) if content else 0}")
            
            return JSONResponse(content=data)
            
    except Exception as e:
        logger.error(f"Chat completion request failed: {str(e)}")
        logger.debug(f"Error type: {type(e)}")
        
        # Return structured error response
        error_response = {
            "error": {
                "message": f"Chat completion failed: {str(e)}",
                "type": "chat_completion_error",
                "code": "request_failed"
            }
        }
        return JSONResponse(content=error_response, status_code=500)


@router.post("/embeddings")
async def embeddings(request: Request) -> Any:
    payload: Dict[str, Any] = await request.json()
    payload.pop("api_key", None)
    data = await _rr_call_json("embeddings", payload, None)
    return JSONResponse(content=data)


@router.post("/images/generations")
async def images_generations(request: Request) -> Any:
    payload: Dict[str, Any] = await request.json()
    payload.pop("api_key", None)
    data = await _rr_call_json("images", payload, None)
    return JSONResponse(content=data)
