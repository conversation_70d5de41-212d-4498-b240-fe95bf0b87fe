from __future__ import annotations
import asyncio
import uvicorn
from dotenv import load_dotenv
from app.core.application import create_app
from extensions.runtime.startup import initialize_extensions
from extensions.web.routes import router as ext_router
from extensions.web.openai_routes import router as ext_openai_router
from extensions.web.admin_routes import router as ext_admin_router


async def main_async() -> None:
    # Load environment variables early (e.g., VERCEL_API_KEYS, GROQ_API_KEYS)
    load_dotenv()
    print("Loading extensions...")
    await initialize_extensions()
    app = create_app()
    # Mount extension routes without touching original app code
    app.include_router(ext_router)
    app.include_router(ext_openai_router)
    app.include_router(ext_admin_router)
    config = uvicorn.Config(app=app, host="0.0.0.0", port=8001, log_level="info")
    server = uvicorn.Server(config)
    await server.serve()


if __name__ == "__main__":
    asyncio.run(main_async())
