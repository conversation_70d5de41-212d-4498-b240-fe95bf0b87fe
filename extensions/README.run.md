Run with provider+proxy orchestration without changing original app:

```bash
python -m extensions.entrypoint
```

Environment variables (optional):
- VERCEL_API_KEYS: comma-separated keys
- GROQ_API_KEYS: comma-separated keys

Notes:
- Proxy pool is auto-crawled and classified at startup.
- Providers are round-robin; each API call will retry next provider on failure.
- Proxy selection is region-aware if proxy region detection is added.
