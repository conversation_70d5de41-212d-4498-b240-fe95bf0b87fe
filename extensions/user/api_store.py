from __future__ import annotations
from typing import Optional
import datetime
import async<PERSON>
import json
from pathlib import Path

from app.database.connection import database
from app.database.models import UserApi<PERSON>ey
from sqlalchemy import select, insert, update, delete


class UserApiStore:
    def __init__(self) -> None:
        self._cache: dict = {}

    async def _load(self) -> None:
        self._cache.clear()
        query = select(UserApiKey)
        db_keys = await database.fetch_all(query)
        self._cache["user_apis"] = [key.api_key for key in db_keys]

    async def set_all(self, apis: list[str], persist: bool = True) -> None:
        # Clear existing keys
        await database.execute(UserApiKey.__table__.delete())

        # Insert new keys
        keys_to_insert = []
        for api_key in apis:
            if api_key and api_key.strip():
                keys_to_insert.append({
                    "api_key": api_key,
                    "status": "active", # Default status
                    "created_at": datetime.datetime.now(),
                    "updated_at": datetime.datetime.now(),
                })
        
        if keys_to_insert:
            await database.execute(insert(UserApiKey).values(keys_to_insert))
        
        # Update in-memory cache
        self._cache["user_apis"] = [x for x in (apis or []) if isinstance(x, str) and x.strip()]

    def list(self) -> list[str]:
        arr = self._cache.get("user_apis")
        if isinstance(arr, list):
            return [str(x).strip() for x in arr if isinstance(x, str) and x.strip()]
        return []


_global_store: Optional[UserApiStore] = None


def get_user_api_store() -> UserApiStore:
    global _global_store
    if _global_store is None:
        _global_store = UserApiStore()
        # 同步加载数据库中的用户API，避免冷启动时丢失
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 无法同步等待时，至少清空缓存，由业务调用方在第一次使用前显式 reload
                pass
            else:
                loop.run_until_complete(_global_store._load())
        except Exception:
            pass
    return _global_store

