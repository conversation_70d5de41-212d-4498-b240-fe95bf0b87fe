#!/usr/bin/env python3
"""
检测热池分配逻辑和数据库操作问题
"""
import asyncio
import sys
import os
from datetime import datetime, timedelta
from sqlalchemy import select, update, func
from app.database.connection import database
from app.database.models import Proxy

async def check_pool_status():
    """检查代理池状态"""
    print("🔍 检查代理池状态...")
    
    # 连接数据库
    await database.connect()
    
    try:
        # 1. 统计各池的代理数量
        pool_stats_query = select(
            Proxy.pool,
            func.count(Proxy.id).label('count')
        ).group_by(Proxy.pool)
        
        pool_stats = await database.fetch_all(pool_stats_query)
        
        print("\n📊 代理池统计:")
        total_proxies = 0
        for stat in pool_stats:
            pool_name = stat.pool or "未分类"
            count = stat.count
            total_proxies += count
            print(f"  {pool_name}: {count} 个代理")
        
        print(f"  总计: {total_proxies} 个代理")
        
        # 2. 检查热池中的代理详情
        warm_query = select(Proxy).where(Proxy.pool == 'warm').limit(10)
        warm_proxies = await database.fetch_all(warm_query)
        
        print(f"\n🔥 热池代理样本 (前10个):")
        for proxy in warm_proxies:
            print(f"  {proxy.proxy_url}")
            print(f"    - 匿名性: {proxy.anonymity}")
            print(f"    - 延迟: {proxy.last_connect_ms}ms")
            print(f"    - 失败次数: {proxy.failure_count}")
            print(f"    - 最后检查: {proxy.last_checked_at}")
            print()
        
        # 3. 检查主池分配情况
        main_query = select(
            Proxy.provider,
            Proxy.api_group,
            func.count(Proxy.id).label('count')
        ).where(Proxy.pool == 'main').group_by(Proxy.provider, Proxy.api_group)
        
        main_stats = await database.fetch_all(main_query)
        
        print("🎯 主池分配统计:")
        if main_stats:
            for stat in main_stats:
                provider = stat.provider or "未知"
                api_group = stat.api_group or "未知"
                count = stat.count
                print(f"  {provider}/{api_group}: {count} 个代理")
        else:
            print("  ❌ 主池为空!")
        
        # 4. 检查失败池情况
        fail_query = select(Proxy).where(Proxy.pool == 'fail').limit(5)
        fail_proxies = await database.fetch_all(fail_query)
        
        print(f"\n❌ 失败池代理样本 (前5个):")
        for proxy in fail_proxies:
            print(f"  {proxy.proxy_url}")
            print(f"    - 失败次数: {proxy.failure_count}")
            print(f"    - 匿名性: {proxy.anonymity}")
            print(f"    - 最后检查: {proxy.last_checked_at}")
            print()
        
        # 5. 检查最近的数据库操作
        recent_query = select(Proxy).order_by(Proxy.last_checked_at.desc()).limit(10)
        recent_proxies = await database.fetch_all(recent_query)
        
        print("⏰ 最近更新的代理 (前10个):")
        for proxy in recent_proxies:
            print(f"  {proxy.proxy_url} ({proxy.pool})")
            print(f"    - 最后检查: {proxy.last_checked_at}")
            print()
        
        # 6. 检查是否有代理卡在热池中
        stuck_query = select(Proxy).where(
            Proxy.pool == 'warm',
            Proxy.last_checked_at < datetime.now() - timedelta(hours=1)
        ).limit(10)
        
        stuck_proxies = await database.fetch_all(stuck_query)
        
        print("🔒 可能卡住的热池代理 (超过1小时未更新):")
        if stuck_proxies:
            for proxy in stuck_proxies:
                print(f"  {proxy.proxy_url}")
                print(f"    - 最后检查: {proxy.last_checked_at}")
                print(f"    - 匿名性: {proxy.anonymity}")
                print(f"    - 延迟: {proxy.last_connect_ms}ms")
                print()
        else:
            print("  ✅ 没有发现卡住的代理")
        
    finally:
        await database.disconnect()

async def check_allocation_logic():
    """检查分配逻辑问题"""
    print("\n🔧 检查分配逻辑...")
    
    await database.connect()
    
    try:
        # 检查符合主池条件但仍在热池的代理
        candidate_query = select(Proxy).where(
            Proxy.pool == 'warm',
            Proxy.last_connect_ms <= 200,  # 延迟≤200ms
            Proxy.anonymity.in_(['anonymous', 'elite', 'high_anonymous']),  # 匿名
            Proxy.failure_count < 3  # 失败次数少
        )
        
        candidates = await database.fetch_all(candidate_query)
        
        print(f"🎯 符合主池条件但仍在热池的代理: {len(candidates)} 个")
        
        if candidates:
            print("  样本:")
            for proxy in candidates[:5]:
                print(f"    {proxy.proxy_url}")
                print(f"      - 延迟: {proxy.last_connect_ms}ms")
                print(f"      - 匿名性: {proxy.anonymity}")
                print(f"      - 失败次数: {proxy.failure_count}")
                print()
        
        # 检查主池中不符合条件的代理
        bad_main_query = select(Proxy).where(
            Proxy.pool == 'main',
            (Proxy.last_connect_ms > 200) | 
            (Proxy.anonymity == 'transparent') |
            (Proxy.failure_count >= 3)
        )
        
        bad_main = await database.fetch_all(bad_main_query)
        
        print(f"❌ 主池中不符合条件的代理: {len(bad_main)} 个")
        
        if bad_main:
            print("  样本:")
            for proxy in bad_main[:5]:
                print(f"    {proxy.proxy_url} ({proxy.provider}/{proxy.api_group})")
                print(f"      - 延迟: {proxy.last_connect_ms}ms")
                print(f"      - 匿名性: {proxy.anonymity}")
                print(f"      - 失败次数: {proxy.failure_count}")
                print()
        
    finally:
        await database.disconnect()

async def main():
    """主函数"""
    print("🚀 开始检测热池分配逻辑...")
    
    await check_pool_status()
    await check_allocation_logic()
    
    print("\n✅ 检测完成!")

if __name__ == "__main__":
    asyncio.run(main())
