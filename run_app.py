import os
import sys
import subprocess
import fcntl
from pathlib import Path

# --- Part 1: Code that can run with any Python ---

PORT = 8001
LOCK_FILE = f"/tmp/my_project_{PORT}.lock"

def kill_process_on_port(port):
    # This function should only use standard library modules.
    # 'lsof' is a system command, so this is fine.
    try:
        result = subprocess.run(['lsof', '-t', f'-i:{port}'], capture_output=True, text=True, check=False)
        pids = result.stdout.strip().split('\n')
        for pid in pids:
            if pid:
                print(f"Found process {pid} on port {port}. Killing it.")
                subprocess.run(['kill', '-9', pid], check=False)
    except FileNotFoundError:
        print("lsof command not found. Skipping process killing.")
    except Exception as e:
        print(f"An error occurred while trying to kill the process on port {port}: {e}")

def get_project_root() -> Path:
    # 项目根目录就是本脚本所在目录
    return Path(__file__).resolve().parent

def ensure_poetry_available() -> None:
    try:
        subprocess.run(["poetry", "--version"], capture_output=True, text=True, check=True)
    except FileNotFoundError:
        print("`poetry` 未安装或不在 PATH 中，请先安装 Poetry: https://python-poetry.org/")
        sys.exit(1)
    except subprocess.CalledProcessError as e:
        print(f"检测 Poetry 失败: {e}")
        sys.exit(1)

def run_background(cmd: list[str], cwd: Path, log_file: Path) -> None:
    log_file.parent.mkdir(parents=True, exist_ok=True)
    with open(log_file, "w") as lf:
        subprocess.Popen(cmd, cwd=str(cwd), stdout=lf, stderr=lf, start_new_session=True)
    print(f"Server starting in the background on port {PORT}.")
    print(f"Logs will be written to {log_file}")

def main():
    # 单实例锁
    lock_fp = open(LOCK_FILE, "w")
    try:
        fcntl.flock(lock_fp, fcntl.LOCK_EX | fcntl.LOCK_NB)
    except (IOError, BlockingIOError):
        print(f"Another instance is already running (lock file {LOCK_FILE} is held). Exiting.")
        sys.exit(1)

    # 释放端口
    kill_process_on_port(PORT)

    # 必须使用 Poetry 启动
    ensure_poetry_available()

    project_root = get_project_root()
    log_path = project_root / "app_background.log"
    cmd = [
        "poetry", "run", "python", "-m", "uvicorn",
        "app.main:app", "--host", "0.0.0.0", "--port", str(PORT)
    ]
    run_background(cmd, project_root, log_path)


def run_app_in_venv():
    # 保留该函数以兼容，但已不再使用
    print("This launcher now always uses 'poetry run uvicorn'.")


if __name__ == "__main__":
    # 始终通过 Poetry 后台启动，可从任意位置调用本脚本
    main()
