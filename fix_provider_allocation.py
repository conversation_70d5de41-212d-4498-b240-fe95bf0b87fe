#!/usr/bin/env python3
"""
修复服务商代理分配问题
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.insert(0, '/home/<USER>/应用/轮询')

async def fix_provider_allocation():
    """修复服务商代理分配问题"""
    print("=== 修复服务商代理分配问题 ===\n")
    
    try:
        from app.database.connection import database
        from app.database.models import Proxy
        from sqlalchemy import select, update, delete
        
        await database.connect()
        
        # 1. 获取所有符合主池条件的代理
        print("📊 步骤1: 获取符合主池条件的代理")
        
        candidates_query = select(Proxy).where(
            (Proxy.last_connect_ms != None) &
            (Proxy.last_connect_ms <= 200) &
            (Proxy.anonymity.in_(['anonymous', 'elite', 'high_anonymous']))
        )
        
        candidates = await database.fetch_all(candidates_query)
        print(f"符合主池条件的代理: {len(candidates)} 个")
        
        if not candidates:
            print("❌ 没有符合条件的代理")
            return
        
        # 按延迟排序
        candidates = sorted(candidates, key=lambda x: x.last_connect_ms or 999)
        
        print("最佳候选代理:")
        for i, proxy in enumerate(candidates[:7]):
            print(f"  {i+1}. {proxy.proxy_url} - {proxy.last_connect_ms}ms - {proxy.region}")
        
        # 2. 清除所有主池记录
        print(f"\n🧹 步骤2: 清除所有主池记录")
        
        # 先将所有主池代理移到热池
        clear_query = update(Proxy).where(Proxy.pool == 'main').values(
            pool='warm',
            provider=None,
            api_group=None
        )
        result = await database.execute(clear_query)
        print(f"✅ 清除了主池记录")
        
        # 3. 为每个服务商创建独立的记录
        print(f"\n🔄 步骤3: 为每个服务商创建独立记录")
        
        # 定义服务商配置
        providers = [
            {
                'name': 'groq',
                'api_groups': ['chat', 'embeddings'],
                'exclude_regions': []
            },
            {
                'name': 'vercel', 
                'api_groups': ['chat', 'embeddings', 'images'],
                'exclude_regions': []
            },
            {
                'name': 'gemini',
                'api_groups': ['chat'],
                'exclude_regions': ['CN']  # gemini排除中国
            }
        ]
        
        # 删除所有现有主池记录
        delete_main_query = delete(Proxy).where(Proxy.pool == 'main')
        await database.execute(delete_main_query)
        print("✅ 删除所有现有主池记录")
        
        # 为每个服务商和API组合创建记录
        from sqlalchemy import insert
        
        total_inserted = 0
        
        for provider in providers:
            provider_name = provider['name']
            api_groups = provider['api_groups']
            exclude_regions = provider['exclude_regions']
            
            print(f"\n--- 处理 {provider_name} ---")
            
            # 过滤候选代理
            filtered_candidates = []
            for candidate in candidates:
                region = (candidate.region or '').upper()
                if region in exclude_regions:
                    continue
                filtered_candidates.append(candidate)
            
            print(f"可用代理: {len(filtered_candidates)} 个")
            
            # 为每个API组创建记录
            for api_group in api_groups:
                for candidate in filtered_candidates:
                    # 创建新的主池记录
                    insert_data = {
                        'proxy_url': candidate.proxy_url,
                        'pool': 'main',
                        'provider': provider_name,
                        'api_group': api_group,
                        'region': candidate.region,
                        'anonymity': candidate.anonymity,
                        'last_connect_ms': candidate.last_connect_ms,
                        'last_e2e_ms': candidate.last_e2e_ms,
                        'last_e2e_ms_by_provider': candidate.last_e2e_ms_by_provider,
                        'failure_count': candidate.failure_count,
                        'last_checked_at': candidate.last_checked_at
                    }
                    
                    insert_query = insert(Proxy).values(**insert_data)
                    await database.execute(insert_query)
                    total_inserted += 1
                
                print(f"  {api_group}: {len(filtered_candidates)} 个代理")
        
        print(f"\n✅ 总共插入 {total_inserted} 条主池记录")
        
        # 4. 验证结果
        print(f"\n📊 步骤4: 验证结果")
        
        from sqlalchemy import func
        
        verify_query = select(
            Proxy.provider,
            Proxy.api_group,
            func.count(Proxy.id).label('count')
        ).where(Proxy.pool == 'main').group_by(Proxy.provider, Proxy.api_group)
        
        verify_results = await database.fetch_all(verify_query)
        
        print("最终分配结果:")
        provider_totals = {}
        
        for row in verify_results:
            provider = row['provider']
            api_group = row['api_group']
            count = row['count']
            
            if provider not in provider_totals:
                provider_totals[provider] = 0
            provider_totals[provider] += count
            
            print(f"  {provider}/{api_group}: {count} 个代理")
        
        print(f"\n各服务商总计:")
        for provider, total in provider_totals.items():
            print(f"  {provider}: {total} 个代理")
        
        # 检查平衡性
        totals = list(provider_totals.values())
        if totals:
            min_total = min(totals)
            max_total = max(totals)
            
            print(f"\n分配分析:")
            print(f"  最少: {min_total}")
            print(f"  最多: {max_total}")
            
            if max_total - min_total <= len(candidates):  # 允许一定差异（因为gemini排除CN）
                print("✅ 分配基本平衡")
            else:
                print("⚠️  分配可能不平衡")
        
        # 5. 显示具体代理
        print(f"\n🔍 步骤5: 显示具体代理分配")
        
        detail_query = select(Proxy).where(Proxy.pool == 'main').limit(15)
        detail_results = await database.fetch_all(detail_query)
        
        print("主池代理详情（前15个）:")
        for i, proxy in enumerate(detail_results):
            print(f"  {i+1}. {proxy.provider}/{proxy.api_group}: {proxy.proxy_url} ({proxy.region})")
        
        print(f"\n🎉 修复完成！")
        print(f"现在每个服务商都应该有相应数量的代理")
        print(f"- groq: 每个API组都有相同数量的代理")
        print(f"- vercel: 每个API组都有相同数量的代理") 
        print(f"- gemini: 排除CN地区后的代理数量")
        print(f"\n请刷新网页查看结果！")
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            await database.disconnect()
        except:
            pass

if __name__ == "__main__":
    asyncio.run(fix_provider_allocation())
