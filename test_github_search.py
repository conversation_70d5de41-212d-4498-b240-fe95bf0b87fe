#!/usr/bin/env python3
"""
测试GitHub搜索查询
"""

import asyncio
import os
import httpx

async def test_github_search():
    """测试不同的GitHub搜索查询"""
    print("=== GitHub搜索查询测试 ===\n")
    
    # 设置Token
    os.environ["GITHUB_TOKEN"] = "*********************************************************************************************"
    
    headers = {
        "Authorization": f"token {os.getenv('GITHUB_TOKEN')}",
        "Accept": "application/vnd.github.v3+json",
    }
    
    # 测试不同的搜索查询
    test_queries = [
        {
            "name": "原始查询",
            "query": "proxy-list+OR+proxies+in:name,description,readme"
        },
        {
            "name": "简化查询1",
            "query": "proxy list"
        },
        {
            "name": "简化查询2", 
            "query": "proxies"
        },
        {
            "name": "具体查询",
            "query": "proxy-list"
        },
        {
            "name": "仓库名查询",
            "query": "proxy in:name"
        },
        {
            "name": "描述查询",
            "query": "proxy in:description"
        }
    ]
    
    url = 'https://api.github.com/search/repositories'
    
    async with httpx.AsyncClient(headers=headers, timeout=30.0) as client:
        for test in test_queries:
            print(f"🔍 测试: {test['name']}")
            print(f"   查询: {test['query']}")
            
            params = {
                'q': test['query'],
                'sort': 'updated',
                'order': 'desc',
                'per_page': 5
            }
            
            try:
                response = await client.get(url, params=params)
                print(f"   状态码: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    total_count = data.get("total_count", 0)
                    items = data.get("items", [])
                    
                    print(f"   总数: {total_count}")
                    print(f"   本页: {len(items)}")
                    
                    if items:
                        print("   仓库示例:")
                        for i, repo in enumerate(items[:3]):
                            print(f"     {i+1}. {repo['full_name']}")
                    else:
                        print("   ❌ 没有结果")
                else:
                    print(f"   ❌ 失败: {response.status_code}")
                    if response.status_code == 403:
                        print(f"   可能是API限制: {response.text}")
                    
            except Exception as e:
                print(f"   ❌ 异常: {e}")
            
            print()

async def test_rate_limit():
    """测试API限制"""
    print("=== API限制测试 ===\n")
    
    headers = {
        "Authorization": f"token {os.getenv('GITHUB_TOKEN')}",
        "Accept": "application/vnd.github.v3+json",
    }
    
    async with httpx.AsyncClient(headers=headers, timeout=30.0) as client:
        try:
            # 测试rate limit端点
            response = await client.get("https://api.github.com/rate_limit")
            print(f"Rate Limit状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                search_limit = data.get("resources", {}).get("search", {})
                print(f"搜索API限制:")
                print(f"  剩余: {search_limit.get('remaining', 'N/A')}")
                print(f"  总数: {search_limit.get('limit', 'N/A')}")
                print(f"  重置时间: {search_limit.get('reset', 'N/A')}")
            else:
                print(f"获取rate limit失败: {response.text}")
                
        except Exception as e:
            print(f"Rate limit测试失败: {e}")

async def test_simple_search():
    """测试最简单的搜索"""
    print("\n=== 最简单搜索测试 ===\n")
    
    headers = {
        "Authorization": f"token {os.getenv('GITHUB_TOKEN')}",
        "Accept": "application/vnd.github.v3+json",
    }
    
    # 最简单的搜索
    url = 'https://api.github.com/search/repositories'
    params = {
        'q': 'proxy',
        'per_page': 5
    }
    
    async with httpx.AsyncClient(headers=headers, timeout=30.0) as client:
        try:
            print("🔍 搜索: proxy")
            response = await client.get(url, params=params)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                total_count = data.get("total_count", 0)
                items = data.get("items", [])
                
                print(f"总仓库数: {total_count}")
                print(f"本页仓库数: {len(items)}")
                
                if items:
                    print("找到的仓库:")
                    for i, repo in enumerate(items):
                        print(f"  {i+1}. {repo['full_name']} - {repo['html_url']}")
                        print(f"      描述: {repo.get('description', 'N/A')}")
                        print(f"      更新: {repo.get('updated_at', 'N/A')}")
                        print()
                else:
                    print("❌ 没有找到仓库")
            else:
                print(f"❌ 搜索失败: {response.status_code}")
                print(f"响应: {response.text}")
                
        except Exception as e:
            print(f"❌ 搜索异常: {e}")

async def main():
    """主函数"""
    await test_rate_limit()
    await test_simple_search()
    await test_github_search()

if __name__ == "__main__":
    asyncio.run(main())
