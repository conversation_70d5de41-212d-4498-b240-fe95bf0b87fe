# Design Document

## Overview

The system has two main issues:
1. **Empty chat responses**: Chat completions return empty responses despite successful model list retrieval
2. **Excessive proxy error logging**: Repeated proxy connection failures are flooding the logs

The root cause analysis shows:
- The proxy system is attempting to connect to Google's Generative Language API through potentially invalid proxies
- The `e2e_preflight_enabled` is set to `false` but the system still performs e2e latency checks
- Proxy errors are not being deduplicated, causing log spam
- Chat requests may be failing due to proxy configuration issues

## Architecture

### Current Flow
1. Client sends request to `/ext/openai/v1/chat/completions`
2. Request is routed through `openai_routes.py` 
3. Monkey patch redirects to provider system via `_rr_call_json/_rr_call_stream`
4. Provider system attempts to use proxy for upstream API calls
5. Proxy selection fails repeatedly, causing errors

### Proposed Changes
1. **Proxy Error Handling**: Implement error deduplication and rate limiting
2. **Fallback Mechanism**: Allow direct connections when proxy fails
3. **Improved Logging**: Structured logging with appropriate levels
4. **Chat Response Debugging**: Add detailed logging for chat completion flow

## Components and Interfaces

### 1. Enhanced Proxy Manager
- **Location**: `extensions/proxy/manager.py`
- **Changes**: Add error deduplication and fallback logic
- **Interface**: 
  ```python
  async def choose_proxy_with_fallback(provider_name: str, api_group: str) -> Optional[str]
  ```

### 2. Logging Deduplication Service
- **Location**: `app/log/deduplication.py` (new)
- **Purpose**: Prevent duplicate log entries
- **Interface**:
  ```python
  class LogDeduplicator:
      def should_log(self, message_key: str, level: str) -> bool
      def record_log(self, message_key: str, level: str) -> None
  ```

### 3. Enhanced Provider HTTP Client
- **Location**: `extensions/runtime/providers_impl.py`
- **Changes**: Add fallback to direct connection, improved error handling
- **Interface**: Maintain existing but add fallback logic

### 4. Chat Response Debugger
- **Location**: `app/service/chat/debug_service.py` (new)
- **Purpose**: Detailed logging for chat completion debugging
- **Interface**:
  ```python
  class ChatDebugService:
      async def log_request_details(self, payload: Dict, provider: str) -> None
      async def log_response_details(self, response: Any, success: bool) -> None
  ```

## Data Models

### Error Deduplication Model
```python
@dataclass
class LogEntry:
    message_key: str
    level: str
    first_occurrence: datetime
    last_occurrence: datetime
    count: int
    suppressed: bool
```

### Proxy Status Model
```python
@dataclass
class ProxyStatus:
    url: str
    last_success: Optional[datetime]
    consecutive_failures: int
    is_blacklisted: bool
    provider_specific_status: Dict[str, bool]
```

## Error Handling

### 1. Proxy Connection Errors
- **Current**: Logs every failure with full stack trace
- **Proposed**: 
  - Log first occurrence with full details
  - Subsequent occurrences within time window get deduplicated
  - Periodic summary logs instead of individual entries
  - Automatic fallback to direct connection

### 2. Chat Completion Errors
- **Current**: Limited error context
- **Proposed**:
  - Log request payload (sanitized)
  - Log provider selection logic
  - Log proxy selection attempts
  - Log upstream response details
  - Clear error categorization (proxy vs API vs parsing)

### 3. Fallback Strategy
```python
async def call_with_fallback(provider: Provider, payload: Dict) -> Dict:
    # Try with proxy first
    try:
        proxy = await choose_proxy(provider.name)
        if proxy:
            return await call_with_proxy(provider, payload, proxy)
    except ProxyError as e:
        log_deduplicator.log_if_needed("proxy_failed", e)
    
    # Fallback to direct connection
    return await call_direct(provider, payload)
```

## Testing Strategy

### 1. Unit Tests
- **Proxy fallback logic**: Test proxy failure scenarios
- **Log deduplication**: Verify duplicate suppression
- **Chat completion flow**: Mock provider responses

### 2. Integration Tests
- **End-to-end chat flow**: Test complete request/response cycle
- **Proxy configuration**: Test various proxy scenarios
- **Error handling**: Test error propagation and logging

### 3. Load Testing
- **Log volume**: Ensure deduplication works under load
- **Proxy switching**: Test proxy pool behavior
- **Chat throughput**: Verify performance with fallback logic

### 4. Manual Testing
- **Chat functionality**: Verify empty response issue is resolved
- **Log readability**: Confirm log noise reduction
- **Error scenarios**: Test various failure modes

## Configuration Changes

### 1. Proxy Configuration
```json
{
  "e2e_preflight_enabled": false,
  "fallback_to_direct": true,
  "max_consecutive_failures": 3,
  "error_deduplication_window_minutes": 5,
  "log_summary_interval_minutes": 15
}
```

### 2. Logging Configuration
```json
{
  "deduplication_enabled": true,
  "deduplication_window_seconds": 300,
  "max_duplicate_count": 10,
  "summary_log_enabled": true
}
```

## Implementation Priority

1. **High Priority**: Fix empty chat responses
2. **High Priority**: Implement proxy fallback mechanism  
3. **Medium Priority**: Add log deduplication
4. **Medium Priority**: Enhance error logging
5. **Low Priority**: Add comprehensive debugging tools