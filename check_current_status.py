#!/usr/bin/env python3
"""
检查当前实际状态
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.insert(0, '/home/<USER>/应用/轮询')

async def check_current_status():
    """检查当前实际状态"""
    print("=== 检查当前实际状态 ===\n")
    
    try:
        from app.database.connection import database
        from app.database.models import Proxy
        from sqlalchemy import select, func
        
        await database.connect()
        
        # 1. 检查各池的代理数量
        print("📊 步骤1: 检查各池的代理数量")
        
        pool_query = select(
            Proxy.pool,
            func.count(Proxy.id).label('count')
        ).group_by(Proxy.pool)
        
        pool_results = await database.fetch_all(pool_query)
        
        print("各池代理数量:")
        for row in pool_results:
            pool = row['pool'] or 'NULL'
            count = row['count']
            print(f"  {pool}: {count:,} 个代理")
        
        # 2. 检查主池分配
        print(f"\n🔍 步骤2: 检查主池分配")
        
        main_query = select(
            Proxy.provider,
            Proxy.api_group,
            func.count(Proxy.id).label('count')
        ).where(Proxy.pool == 'main').group_by(Proxy.provider, Proxy.api_group)
        
        main_results = await database.fetch_all(main_query)
        
        if main_results:
            print("主池分配:")
            provider_totals = {}
            for row in main_results:
                provider = row['provider'] or 'NULL'
                api_group = row['api_group'] or 'NULL'
                count = row['count']
                
                if provider not in provider_totals:
                    provider_totals[provider] = 0
                provider_totals[provider] += count
                
                print(f"  {provider}/{api_group}: {count} 个代理")
            
            print(f"\n各服务商总计:")
            for provider, total in provider_totals.items():
                print(f"  {provider}: {total} 个代理")
        else:
            print("❌ 主池为空")
        
        # 3. 检查符合主池条件的代理
        print(f"\n🔍 步骤3: 检查符合主池条件的代理")
        
        # 延迟≤200ms的代理
        low_latency_query = select(func.count(Proxy.id)).where(
            (Proxy.last_connect_ms != None) & (Proxy.last_connect_ms <= 200)
        )
        low_latency_count = await database.fetch_val(low_latency_query)
        
        # 匿名代理
        anonymous_query = select(func.count(Proxy.id)).where(
            Proxy.anonymity.in_(['anonymous', 'elite', 'high_anonymous'])
        )
        anonymous_count = await database.fetch_val(anonymous_query)
        
        # 同时满足两个条件的代理
        qualified_query = select(func.count(Proxy.id)).where(
            (Proxy.last_connect_ms != None) & 
            (Proxy.last_connect_ms <= 200) &
            (Proxy.anonymity.in_(['anonymous', 'elite', 'high_anonymous']))
        )
        qualified_count = await database.fetch_val(qualified_query)
        
        print(f"延迟≤200ms的代理: {low_latency_count:,} 个")
        print(f"匿名代理: {anonymous_count:,} 个")
        print(f"符合主池条件的代理: {qualified_count:,} 个")
        
        # 4. 检查匿名性分布
        print(f"\n📊 步骤4: 检查匿名性分布")
        
        anonymity_query = select(
            Proxy.anonymity,
            func.count(Proxy.id).label('count')
        ).group_by(Proxy.anonymity)
        
        anonymity_results = await database.fetch_all(anonymity_query)
        
        print("匿名性分布:")
        for row in anonymity_results:
            anonymity = row['anonymity'] or 'NULL'
            count = row['count']
            print(f"  {anonymity}: {count:,} 个代理")
        
        # 5. 检查延迟分布
        print(f"\n⏱️ 步骤5: 检查延迟分布")
        
        # 按延迟范围统计
        from sqlalchemy import text
        
        latency_query = text("""
            SELECT 
                CASE 
                    WHEN last_connect_ms IS NULL THEN 'NULL'
                    WHEN last_connect_ms <= 50 THEN '≤50ms'
                    WHEN last_connect_ms <= 100 THEN '51-100ms'
                    WHEN last_connect_ms <= 200 THEN '101-200ms'
                    WHEN last_connect_ms <= 500 THEN '201-500ms'
                    WHEN last_connect_ms <= 1000 THEN '501-1000ms'
                    ELSE '>1000ms'
                END as latency_range,
                COUNT(*) as count
            FROM t_proxies 
            GROUP BY latency_range
            ORDER BY 
                CASE 
                    WHEN last_connect_ms IS NULL THEN 999
                    ELSE last_connect_ms
                END
        """)
        
        latency_results = await database.fetch_all(latency_query)
        
        print("延迟分布:")
        for row in latency_results:
            range_name = row['latency_range']
            count = row['count']
            print(f"  {range_name}: {count:,} 个代理")
        
        # 6. 分析问题
        print(f"\n🔍 步骤6: 问题分析")
        
        print("问题分析:")
        
        if qualified_count == 0:
            print("❌ 没有符合主池条件的代理")
            print("可能原因:")
            print("1. 代理延迟过高（>200ms）")
            print("2. 匿名性检测失败或不符合要求")
            print("3. 代理质量较差")
            
            # 检查是否有延迟数据
            has_latency_query = select(func.count(Proxy.id)).where(
                Proxy.last_connect_ms != None
            )
            has_latency_count = await database.fetch_val(has_latency_query)
            
            print(f"\n有延迟数据的代理: {has_latency_count:,} 个")
            
            if has_latency_count == 0:
                print("⚠️  没有代理有延迟数据，可能分类过程还在进行中")
            
        elif qualified_count > 0:
            print(f"✅ 有 {qualified_count} 个符合条件的代理")
            
            # 检查为什么没有分配到主池
            main_total = sum(row['count'] for row in main_results) if main_results else 0
            
            if main_total == 0:
                print("❌ 但是主池为空，可能原因:")
                print("1. 主池构建还未触发")
                print("2. 主池构建过程中出现错误")
                print("3. 服务商配置问题")
            elif main_total < qualified_count:
                print(f"⚠️  主池只有 {main_total} 个代理，少于符合条件的 {qualified_count} 个")
                print("可能原因:")
                print("1. 主池构建逻辑有问题")
                print("2. 服务商地区限制过于严格")
        
        # 7. 建议操作
        print(f"\n💡 建议操作:")
        
        if qualified_count == 0:
            print("1. 等待代理分类和匿名性检测完成")
            print("2. 检查代理源质量")
            print("3. 考虑降低主池延迟阈值")
        elif qualified_count > 0:
            main_total = sum(row['count'] for row in main_results) if main_results else 0
            if main_total == 0:
                print("1. 手动触发主池构建")
                print("2. 检查服务商配置")
                print("3. 查看主池构建日志")
            else:
                print("1. 检查服务商分配是否平衡")
                print("2. 验证代理选择功能")
        
        print(f"\n🔧 手动操作命令:")
        print("# 手动触发主池构建")
        print("curl -X POST http://localhost:8001/admin/proxy/refresh")
        print("\n# 查看代理池状态")
        print("curl http://localhost:8001/admin/proxy/status")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            await database.disconnect()
        except:
            pass

if __name__ == "__main__":
    asyncio.run(check_current_status())
