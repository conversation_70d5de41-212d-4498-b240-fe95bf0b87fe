#!/usr/bin/env python3
"""
最终修复服务商代理分配问题
使用更新策略而不是插入重复记录
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.insert(0, '/home/<USER>/应用/轮询')

async def final_fix_allocation():
    """最终修复服务商代理分配问题"""
    print("=== 最终修复服务商代理分配问题 ===\n")
    
    try:
        from app.database.connection import database
        from app.database.models import Proxy
        from sqlalchemy import select, update, func
        
        await database.connect()
        
        # 1. 获取所有符合主池条件的代理
        print("📊 步骤1: 获取符合主池条件的代理")
        
        candidates_query = select(Proxy).where(
            (Proxy.last_connect_ms != None) &
            (Proxy.last_connect_ms <= 200) &
            (Proxy.anonymity.in_(['anonymous', 'elite', 'high_anonymous']))
        )
        
        candidates = await database.fetch_all(candidates_query)
        print(f"符合主池条件的代理: {len(candidates)} 个")
        
        if not candidates:
            print("❌ 没有符合条件的代理")
            return
        
        # 按延迟排序
        candidates = sorted(candidates, key=lambda x: x.last_connect_ms or 999)
        
        print("候选代理:")
        for i, proxy in enumerate(candidates):
            print(f"  {i+1}. {proxy.proxy_url} - {proxy.last_connect_ms}ms - {proxy.region}")
        
        # 2. 清除所有主池分配
        print(f"\n🧹 步骤2: 清除所有主池分配")
        
        clear_query = update(Proxy).where(Proxy.pool == 'main').values(
            pool='warm',
            provider=None,
            api_group=None
        )
        await database.execute(clear_query)
        print("✅ 清除了所有主池分配")
        
        # 3. 重新分配代理到主池
        print(f"\n🔄 步骤3: 重新分配代理到主池")
        
        # 定义服务商配置
        providers = {
            'groq': {
                'api_groups': ['chat', 'embeddings'],
                'exclude_regions': []
            },
            'vercel': {
                'api_groups': ['chat', 'embeddings', 'images'],
                'exclude_regions': []
            },
            'gemini': {
                'api_groups': ['chat'],
                'exclude_regions': ['CN']  # gemini排除中国
            }
        }
        
        allocation_results = {}
        
        for provider_name, config in providers.items():
            print(f"\n--- 分配给 {provider_name} ---")
            
            api_groups = config['api_groups']
            exclude_regions = config['exclude_regions']
            
            # 过滤候选代理
            filtered_candidates = []
            excluded_count = 0
            
            for candidate in candidates:
                region = (candidate.region or '').upper()
                if region in exclude_regions:
                    excluded_count += 1
                    continue
                filtered_candidates.append(candidate)
            
            print(f"总候选: {len(candidates)}")
            print(f"排除: {excluded_count} 个（地区: {exclude_regions}）")
            print(f"可分配: {len(filtered_candidates)} 个")
            
            # 为第一个API组分配代理到主池
            if filtered_candidates and api_groups:
                primary_api = api_groups[0]  # 使用第一个API组作为主要分配
                
                assigned_count = 0
                for candidate in filtered_candidates:
                    # 更新代理到主池
                    update_query = update(Proxy).where(
                        Proxy.proxy_url == candidate.proxy_url
                    ).values(
                        pool='main',
                        provider=provider_name,
                        api_group=primary_api
                    )
                    
                    result = await database.execute(update_query)
                    if result:
                        assigned_count += 1
                
                print(f"✅ 分配了 {assigned_count} 个代理到 {provider_name}/{primary_api}")
                allocation_results[provider_name] = assigned_count
        
        # 4. 验证分配结果
        print(f"\n📊 步骤4: 验证分配结果")
        
        verify_query = select(
            Proxy.provider,
            Proxy.api_group,
            func.count(Proxy.id).label('count')
        ).where(Proxy.pool == 'main').group_by(Proxy.provider, Proxy.api_group)
        
        verify_results = await database.fetch_all(verify_query)
        
        print("数据库验证结果:")
        provider_totals = {}
        
        for row in verify_results:
            provider = row['provider']
            api_group = row['api_group']
            count = row['count']
            
            if provider not in provider_totals:
                provider_totals[provider] = 0
            provider_totals[provider] += count
            
            print(f"  {provider}/{api_group}: {count} 个代理")
        
        print(f"\n各服务商总计:")
        for provider, total in provider_totals.items():
            print(f"  {provider}: {total} 个代理")
        
        # 5. 检查分配平衡性
        print(f"\n⚖️  步骤5: 检查分配平衡性")
        
        expected_groq = len([c for c in candidates if (c.region or '').upper() not in []])
        expected_vercel = len([c for c in candidates if (c.region or '').upper() not in []])
        expected_gemini = len([c for c in candidates if (c.region or '').upper() not in ['CN']])
        
        print(f"期望分配:")
        print(f"  groq: {expected_groq} 个代理")
        print(f"  vercel: {expected_vercel} 个代理")
        print(f"  gemini: {expected_gemini} 个代理")
        
        actual_groq = provider_totals.get('groq', 0)
        actual_vercel = provider_totals.get('vercel', 0)
        actual_gemini = provider_totals.get('gemini', 0)
        
        print(f"\n实际分配:")
        print(f"  groq: {actual_groq} 个代理")
        print(f"  vercel: {actual_vercel} 个代理")
        print(f"  gemini: {actual_gemini} 个代理")
        
        # 检查是否符合预期
        if actual_groq == expected_groq and actual_vercel == expected_vercel and actual_gemini == expected_gemini:
            print("✅ 分配完全符合预期！")
        else:
            print("⚠️  分配与预期有差异")
        
        # 6. 显示具体分配
        print(f"\n🔍 步骤6: 显示具体分配")
        
        detail_query = select(Proxy).where(Proxy.pool == 'main').order_by(Proxy.provider, Proxy.proxy_url)
        detail_results = await database.fetch_all(detail_query)
        
        print("主池代理详情:")
        current_provider = None
        for proxy in detail_results:
            if proxy.provider != current_provider:
                current_provider = proxy.provider
                print(f"\n{current_provider}:")
            
            print(f"  {proxy.proxy_url} ({proxy.region}) - {proxy.last_connect_ms}ms")
        
        print(f"\n🎉 修复完成！")
        print(f"现在的分配策略:")
        print(f"- 每个服务商使用相同的优质代理池")
        print(f"- groq和vercel: 无地区限制，使用所有{len(candidates)}个代理")
        print(f"- gemini: 排除CN地区，使用{len([c for c in candidates if (c.region or '').upper() != 'CN'])}个代理")
        print(f"- 同一个IP可以被多个服务商使用")
        print(f"\n请刷新网页查看结果！")
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            await database.disconnect()
        except:
            pass

if __name__ == "__main__":
    asyncio.run(final_fix_allocation())
