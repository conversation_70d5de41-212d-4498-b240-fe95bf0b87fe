#!/usr/bin/env python3
"""
简化的缓存测试
"""

import asyncio
import json
import os
from pathlib import Path

async def test_simple_cache():
    """简化的缓存测试"""
    print("=== 简化缓存测试 ===\n")
    
    # 1. 设置环境
    os.environ["GITHUB_TOKEN"] = "*********************************************************************************************"
    
    # 2. 清理缓存
    cache_file = Path("extensions/proxy/repo_analysis_cache.json")
    if cache_file.exists():
        cache_file.unlink()
        print("🗑️  已清理现有缓存")
    
    # 3. 手动创建测试仓库列表
    test_repos = [
        "https://github.com/databay-labs/free-proxy-list",
        "https://github.com/hookzof/socks5_list", 
        "https://github.com/zloi-user/hideip.me"
    ]
    
    print(f"📦 测试仓库: {len(test_repos)} 个")
    for i, repo in enumerate(test_repos):
        print(f"  {i+1}. {repo}")
    
    # 4. 直接调用分析函数
    print(f"\n🔬 开始分析仓库...")
    
    try:
        from extensions.proxy.crawlers.site_crawler import analyze_github_repository
        
        cache_data = {}
        
        for repo_url in test_repos:
            print(f"   分析: {repo_url}")
            
            try:
                result = await analyze_github_repository(repo_url)
                repo_name = repo_url.replace("https://github.com/", "")
                
                cache_data[repo_name] = {
                    "status": result["status"],
                    "last_analyzed": asyncio.get_event_loop().time()
                }
                
                print(f"     结果: {result['status']}")
                
            except Exception as e:
                print(f"     失败: {e}")
                repo_name = repo_url.replace("https://github.com/", "")
                cache_data[repo_name] = {
                    "status": "invalid",
                    "last_analyzed": asyncio.get_event_loop().time()
                }
        
        # 5. 手动保存缓存
        if cache_data:
            cache_file.write_text(json.dumps(cache_data, indent=2), encoding="utf-8")
            print(f"\n💾 缓存已保存: {len(cache_data)} 个仓库")
            
            valid_count = sum(1 for data in cache_data.values() if data.get("status") == "valid")
            invalid_count = sum(1 for data in cache_data.values() if data.get("status") == "invalid")
            
            print(f"   - 有效仓库: {valid_count}")
            print(f"   - 无效仓库: {invalid_count}")
            
            print(f"\n   缓存内容:")
            for repo_name, data in cache_data.items():
                print(f"     {repo_name}: {data}")
            
            return True
        else:
            print(f"\n❌ 没有生成缓存数据")
            return False
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

async def test_cache_loading():
    """测试缓存加载"""
    print(f"\n=== 缓存加载测试 ===")
    
    try:
        from extensions.proxy.crawlers.github_crawler import _load_analyzed_repos
        
        analyzed_repos = _load_analyzed_repos()
        print(f"加载的已分析仓库: {len(analyzed_repos)}")
        
        if analyzed_repos:
            print("已分析仓库:")
            for i, repo in enumerate(analyzed_repos):
                print(f"  {i+1}. {repo}")
            return True
        else:
            print("❌ 没有加载到已分析仓库")
            return False
            
    except Exception as e:
        print(f"❌ 缓存加载失败: {e}")
        return False

async def test_skip_logic():
    """测试跳过逻辑"""
    print(f"\n=== 跳过逻辑测试 ===")
    
    try:
        from extensions.proxy.crawlers.github_crawler import _load_analyzed_repos, search_proxy_repositories
        
        # 加载已分析仓库
        analyzed_repos = _load_analyzed_repos()
        print(f"已分析仓库: {len(analyzed_repos)}")
        
        if not analyzed_repos:
            print("❌ 没有已分析仓库，无法测试跳过逻辑")
            return False
        
        # 搜索新仓库（应该跳过已分析的）
        print("搜索新仓库（应该跳过已分析的）...")
        new_repos = await search_proxy_repositories(analyzed_repos, max_pages=1)
        
        print(f"新发现仓库: {len(new_repos)}")
        
        # 检查是否有重叠
        overlap_count = 0
        for repo_url in new_repos[:10]:  # 只检查前10个
            repo_name = repo_url.replace("https://github.com/", "")
            if repo_name in analyzed_repos:
                overlap_count += 1
                print(f"  ⚠️  重叠: {repo_name}")
        
        if overlap_count == 0:
            print("✅ 跳过逻辑正常：没有重叠仓库")
            return True
        else:
            print(f"❌ 跳过逻辑有问题：{overlap_count} 个重叠仓库")
            return False
            
    except Exception as e:
        print(f"❌ 跳过逻辑测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("开始简化的GitHub缓存测试...\n")
    
    # 测试缓存生成
    cache_success = await test_simple_cache()
    
    if cache_success:
        # 测试缓存加载
        load_success = await test_cache_loading()
        
        if load_success:
            # 测试跳过逻辑
            skip_success = await test_skip_logic()
            
            print(f"\n" + "="*60)
            if cache_success and load_success and skip_success:
                print("🎉 所有测试通过！")
                print("="*60)
                
                print(f"\n📋 验证结果:")
                print("✅ GitHub仓库缓存生成成功")
                print("✅ 有效和无效仓库都被缓存")
                print("✅ 缓存加载功能正常")
                print("✅ 缓存跳过逻辑正常工作")
                print("✅ 缓存逻辑完全符合要求")
            else:
                print("❌ 部分测试失败")
                print("="*60)
        else:
            print(f"\n❌ 缓存加载测试失败")
    else:
        print(f"\n❌ 缓存生成测试失败")

if __name__ == "__main__":
    asyncio.run(main())
