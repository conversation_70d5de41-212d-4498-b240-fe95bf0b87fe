"""
Response validation module for chat completions
"""
from typing import Dict, <PERSON>, Optional, <PERSON>ple
from app.log.logger import get_main_logger

logger = get_main_logger()


class ChatResponseValidator:
    """Validates chat completion responses and ensures they contain content"""
    
    @staticmethod
    def validate_response(response: Dict[str, Any], provider_name: str) -> <PERSON><PERSON>[bool, Optional[str]]:
        """
        Validate that a chat completion response contains actual content.
        
        Args:
            response: The response dictionary from the provider
            provider_name: Name of the provider that generated the response
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not isinstance(response, dict):
            return False, f"Response is not a dictionary: {type(response)}"
        
        # Check for error responses first
        if 'error' in response:
            error_info = response['error']
            if isinstance(error_info, dict):
                error_msg = error_info.get('message', 'Unknown error')
                error_code = error_info.get('code', 'unknown')
                return False, f"Provider error ({error_code}): {error_msg}"
            else:
                return False, f"Provider error: {error_info}"
        
        # Validate OpenAI-compatible format (groq, vercel, etc.)
        if 'choices' in response:
            return ChatResponseValidator._validate_openai_format(response, provider_name)
        
        # Validate Gemini format
        elif 'candidates' in response:
            return ChatResponseValidator._validate_gemini_format(response, provider_name)
        
        # Unknown format
        else:
            logger.warning(f"Unknown response format from {provider_name}: {list(response.keys())}")
            return False, f"Unknown response format from {provider_name}"
    
    @staticmethod
    def _validate_openai_format(response: Dict[str, Any], provider_name: str) -> Tuple[bool, Optional[str]]:
        """Validate OpenAI-compatible response format"""
        choices = response.get('choices', [])
        
        if not choices:
            return False, f"No choices in response from {provider_name}"
        
        if not isinstance(choices, list):
            return False, f"Choices is not a list in response from {provider_name}"
        
        # Check first choice for content
        first_choice = choices[0]
        if not isinstance(first_choice, dict):
            return False, f"First choice is not a dictionary in response from {provider_name}"
        
        # Check for message content
        message = first_choice.get('message', {})
        if not isinstance(message, dict):
            return False, f"Message is not a dictionary in response from {provider_name}"
        
        content = message.get('content')
        if content is None:
            return False, f"No content in message from {provider_name}"
        
        if not isinstance(content, str):
            return False, f"Content is not a string in response from {provider_name}: {type(content)}"
        
        if not content.strip():
            return False, f"Empty content in response from {provider_name}"
        
        logger.debug(f"Valid OpenAI format response from {provider_name}, content length: {len(content)}")
        return True, None
    
    @staticmethod
    def _validate_gemini_format(response: Dict[str, Any], provider_name: str) -> Tuple[bool, Optional[str]]:
        """Validate Gemini response format"""
        candidates = response.get('candidates', [])
        
        if not candidates:
            return False, f"No candidates in response from {provider_name}"
        
        if not isinstance(candidates, list):
            return False, f"Candidates is not a list in response from {provider_name}"
        
        # Check first candidate for content
        first_candidate = candidates[0]
        if not isinstance(first_candidate, dict):
            return False, f"First candidate is not a dictionary in response from {provider_name}"
        
        # Check for content parts
        content = first_candidate.get('content', {})
        if not isinstance(content, dict):
            return False, f"Content is not a dictionary in response from {provider_name}"
        
        parts = content.get('parts', [])
        if not parts:
            return False, f"No parts in content from {provider_name}"
        
        if not isinstance(parts, list):
            return False, f"Parts is not a list in response from {provider_name}"
        
        # Check first part for text
        first_part = parts[0]
        if not isinstance(first_part, dict):
            return False, f"First part is not a dictionary in response from {provider_name}"
        
        text = first_part.get('text')
        if text is None:
            return False, f"No text in first part from {provider_name}"
        
        if not isinstance(text, str):
            return False, f"Text is not a string in response from {provider_name}: {type(text)}"
        
        if not text.strip():
            return False, f"Empty text in response from {provider_name}"
        
        logger.debug(f"Valid Gemini format response from {provider_name}, text length: {len(text)}")
        return True, None
    
    @staticmethod
    def extract_content(response: Dict[str, Any]) -> Optional[str]:
        """
        Extract content from a validated response.
        
        Args:
            response: The validated response dictionary
            
        Returns:
            The extracted content string, or None if not found
        """
        # OpenAI format
        if 'choices' in response and response['choices']:
            message = response['choices'][0].get('message', {})
            return message.get('content')
        
        # Gemini format
        elif 'candidates' in response and response['candidates']:
            content = response['candidates'][0].get('content', {})
            parts = content.get('parts', [])
            if parts:
                return parts[0].get('text')
        
        return None
    
    @staticmethod
    def create_error_response(error_message: str, provider_name: str) -> Dict[str, Any]:
        """
        Create a standardized error response.
        
        Args:
            error_message: The error message
            provider_name: Name of the provider that failed
            
        Returns:
            A standardized error response dictionary
        """
        return {
            "error": {
                "message": f"Chat completion failed: {error_message}",
                "type": "chat_completion_error",
                "provider": provider_name,
                "code": "empty_response"
            }
        }