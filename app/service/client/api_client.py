# app/services/chat/api_client.py

from typing import Dict, Any, AsyncGenerator, Optional, List
from datetime import datetime
import httpx
from abc import ABC, abstractmethod
from app.config.config import settings
from app.log.logger import get_api_client_logger
from app.core.constants import DEFAULT_TIMEOUT, API_VERSION

# Proxy system has been removed
PROXY_SYSTEM_AVAILABLE = False

logger = get_api_client_logger()

_all_api_clients: List["ApiClient"] = []

class ApiClient(ABC):
    """API客户端基类"""

    def __init__(self, timeout: int = DEFAULT_TIMEOUT):
        self._proxy_manager = None
        self._connection_enforcer = None
        self._httpx_client = httpx.AsyncClient(timeout=timeout, verify=False)
        if PROXY_SYSTEM_AVAILABLE:
            self._proxy_manager = get_proxy_manager()
            self._connection_enforcer = get_connection_enforcer()
        _all_api_clients.append(self)

    async def close(self):
        await self._httpx_client.aclose()

    async def _get_proxy(self, provider: str, api_group: str) -> Optional[str]:
        if not self._proxy_manager:
            return None
        logger.debug(f"Attempting to choose proxy for provider: {provider}, api_group: {api_group}")
        start_time = datetime.now()
        proxy = await self._proxy_manager.choose_proxy(provider=provider, api_group=api_group)
        end_time = datetime.now()
        logger.debug(f"Proxy choice for {provider}/{api_group} completed in {(end_time - start_time).total_seconds()*1000:.2f} ms. Chosen proxy: {proxy if proxy else 'None'}")
        return proxy

    async def _report_failure(self, proxy_url: str, provider: str):
        if self._proxy_manager:
            await self._proxy_manager.report_failure(proxy_url=proxy_url, provider=provider)

async def close_all_api_clients():
    for client in _all_api_clients:
        await client.close()

    @abstractmethod
    async def generate_content(self, *args, **kwargs) -> Dict[str, Any]:
        pass

    @abstractmethod
    async def stream_generate_content(self, *args, **kwargs) -> AsyncGenerator[str, None]:
        pass


class GeminiApiClient(ApiClient):
    """Gemini API客户端"""

    def __init__(self, base_url: str, timeout: int = DEFAULT_TIMEOUT):
        super().__init__(timeout=timeout)
        bu = base_url.rstrip('/')
        if not bu.endswith(f"/v1") and not bu.endswith(f"/v1beta"):
            bu = f"{bu}/{API_VERSION}"
        self.base_url = bu

    def _prepare_headers(self) -> Dict[str, str]:
        headers = {}
        if settings.CUSTOM_HEADERS:
            headers.update(settings.CUSTOM_HEADERS)
        return headers

    async def generate_content(self, payload: Dict[str, Any], model: str, api_key: str, provider: str, api_group: str) -> Dict[str, Any]:
        proxy_to_use = await self._get_proxy(provider, api_group)
        logger.info(f"Using proxy for Gemini ({provider}/{api_group}): {proxy_to_use}")

        headers = self._prepare_headers()
        
        try:
            # Use the shared httpx client
            client = self._httpx_client
            if proxy_to_use:
                client.proxies = {"all://": proxy_to_use}
            else:
                client.proxies = {}

            normalized_model = model.split("/", 1)[1] if isinstance(model, str) and model.startswith("google/") else model
            url = f"{self.base_url}/models/{normalized_model}:generateContent?key={api_key}"
            logger.info(f"Sending request to Gemini URL: {url} with proxy: {proxy_to_use}")
            start_time = datetime.now()
            response = await client.post(url, json=payload, headers=headers)
            end_time = datetime.now()
            logger.info(f"Gemini request to {url} completed with status: {response.status_code} in {(end_time - start_time).total_seconds()*1000:.2f} ms.")
            response.raise_for_status()
            return response.json()
        except (httpx.RequestError, httpx.HTTPStatusError) as e:
            logger.error(f"Request to Gemini failed for provider {provider} with proxy {proxy_to_use}. Error: {e}. URL: {self.base_url}/models/{model}:generateContent. Payload: {payload}", exc_info=True)
            if proxy_to_use:
                await self._report_failure(proxy_to_use, provider)
            raise

    async def stream_generate_content(self, payload: Dict[str, Any], model: str, api_key: str, provider: str, api_group: str) -> AsyncGenerator[str, None]:
        proxy_to_use = await self._get_proxy(provider, api_group)
        logger.info(f"Using proxy for Gemini Stream ({provider}/{api_group}): {proxy_to_use}")
        
        headers = self._prepare_headers()
        
        try:
            # Use the shared httpx client
            client = self._httpx_client
            if proxy_to_use:
                client.proxies = {"all://": proxy_to_use}
            else:
                client.proxies = {}

            normalized_model = model.split("/", 1)[1] if isinstance(model, str) and model.startswith("google/") else model
            url = f"{self.base_url}/models/{normalized_model}:streamGenerateContent?alt=sse&key={api_key}"
            logger.info(f"Sending stream request to Gemini URL: {url} with proxy: {proxy_to_use}")
            start_time = datetime.now()
            async with client.stream("POST", url, json=payload, headers=headers) as response:
                end_time = datetime.now()
                logger.info(f"Gemini stream request to {url} initiated with status: {response.status_code} in {(end_time - start_time).total_seconds()*1000:.2f} ms.")
                response.raise_for_status()
                async for line in response.aiter_lines():
                    yield line
        except (httpx.RequestError, httpx.HTTPStatusError) as e:
            logger.error(f"Stream request to Gemini failed for provider {provider} with proxy {proxy_to_use}. Error: {e}. URL: {self.base_url}/models/{model}:streamGenerateContent. Payload: {payload}", exc_info=True)
            if proxy_to_use:
                await self._report_failure(proxy_to_use, provider)
            raise


class OpenaiApiClient(ApiClient):
    """OpenAI API客户端"""

    def __init__(self, base_url: str, timeout: int = DEFAULT_TIMEOUT):
        super().__init__(timeout=timeout)
        self.base_url = base_url
        
    def _prepare_headers(self, api_key: str) -> Dict[str, str]:
        headers = {"Authorization": f"Bearer {api_key}"}
        if settings.CUSTOM_HEADERS:
            headers.update(settings.CUSTOM_HEADERS)
        return headers

    async def generate_content(self, payload: Dict[str, Any], api_key: str, provider: str, api_group: str) -> Dict[str, Any]:
        proxy_to_use = await self._get_proxy(provider, api_group)
        logger.info(f"Using proxy for OpenAI ({provider}/{api_group}): {proxy_to_use}")

        headers = self._prepare_headers(api_key)
        
        try:
            # Use the shared httpx client
            client = self._httpx_client
            if proxy_to_use:
                client.proxies = {"all://": proxy_to_use}
            else:
                client.proxies = {}

            # Expect base_url to already point to an OpenAI-compatible /v1 root (e.g., http://127.0.0.1:8001/ext/openai/v1)
            url = f"{self.base_url}/chat/completions"
            logger.info(f"Sending request to OpenAI URL: {url} with proxy: {proxy_to_use}")
            start_time = datetime.now()
            response = await client.post(url, json=payload, headers=headers)
            end_time = datetime.now()
            logger.info(f"OpenAI request to {url} completed with status: {response.status_code} in {(end_time - start_time).total_seconds()*1000:.2f} ms.")
            response.raise_for_status()
            return response.json()
        except (httpx.RequestError, httpx.HTTPStatusError) as e:
            logger.error(f"Request to OpenAI failed for provider {provider} with proxy {proxy_to_use}. Error: {e}. URL: {self.base_url}/chat/completions. Payload: {payload}", exc_info=True)
            if proxy_to_use:
                await self._report_failure(proxy_to_use, provider)
            raise

    async def stream_generate_content(self, payload: Dict[str, Any], api_key: str, provider: str, api_group: str) -> AsyncGenerator[str, None]:
        proxy_to_use = await self._get_proxy(provider, api_group)
        logger.info(f"Using proxy for OpenAI Stream ({provider}/{api_group}): {proxy_to_use}")

        headers = self._prepare_headers(api_key)

        try:
            # Use the shared httpx client
            client = self._httpx_client
            if proxy_to_use:
                client.proxies = {"all://": proxy_to_use}
            else:
                client.proxies = {}

            # Expect base_url to already be an OpenAI-compatible /v1 root
            url = f"{self.base_url}/chat/completions"
            logger.info(f"Sending stream request to OpenAI URL: {url} with proxy: {proxy_to_use}")
            start_time = datetime.now()
            async with client.stream("POST", url, json=payload, headers=headers) as response:
                end_time = datetime.now()
                logger.info(f"OpenAI stream request to {url} initiated with status: {response.status_code} in {(end_time - start_time).total_seconds()*1000:.2f} ms.")
                response.raise_for_status()
                async for line in response.aiter_lines():
                    yield line
        except (httpx.RequestError, httpx.HTTPStatusError) as e:
            logger.error(f"Stream request to OpenAI failed for provider {provider} with proxy {proxy_to_use}. Error: {e}. URL: {self.base_url}/chat/completions. Payload: {payload}", exc_info=True)
            if proxy_to_use:
                await self._report_failure(proxy_to_use, provider)
            raise
