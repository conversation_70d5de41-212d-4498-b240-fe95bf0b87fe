{% extends "base.html" %}
{% block content %}
<h2>服务商管理 - Patched</h2>
<div class="glass-card p-3 rounded space-y-3 text-sm">
  <div>
    <strong>统一用户端 API Base:</strong>
    <code id="gateway-base"></code>
    <button onclick="copyGateway()" class="bg-gray-200 px-2 py-0.5 rounded">复制</button>
  </div>
  <div class="flex gap-2 items-end">
    <a href="/ext/admin/providers" class="bg-gray-600 text-white px-2 py-1 rounded">打开多服务商 Providers 管理</a>
    <div class="ml-auto flex gap-2 items-end">
      <label class="text-xs">用户 API Keys(多行) <textarea id="user-api" class="form-input" placeholder="一行一个" style="width:22rem;height:6rem"></textarea></label>
      <button onclick="saveUserApi()" class="bg-blue-600 text-white px-2 py-1 rounded">保存</button>
      <label class="text-xs">Prompt <input id="test-prompt" class="form-input" value="Hello" style="width:14rem"/></label>
      <select id="test-api" class="form-input text-xs">
        <option value="">选择一个用户API测试…</option>
      </select>
      <button onclick="testUserApi()" class="bg-green-600 text-white px-2 py-1 rounded">用统一Base连通测试</button>
    </div>
  </div>
  <div id="providers" class="mt-2"></div>
</div>
<div class="mt-3">
  <details open>
    <summary class="cursor-pointer">疑似失效的 Keys（≥3次）</summary>
    <div id="invalid-keys" class="mt-2 text-xs"></div>
  </details>
</div>

<script>
function gatewayBase(){
  const loc = window.location;
  // 使用扩展的 OpenAI 兼容入口，允许在 payload 中传 user api_key
  return loc.origin + '/ext/openai/v1';
}
window.gatewayBase = gatewayBase;

function copyGateway(){
  const txt = document.getElementById('gateway-base').textContent||"";
  if(!txt){ alert('无可复制的URL'); return; }
  if(navigator.clipboard && navigator.clipboard.writeText){
    navigator.clipboard.writeText(txt).then(()=>alert('已复制')).catch(()=>{
      const ta=document.createElement('textarea'); ta.value=txt; document.body.appendChild(ta); ta.select(); try{document.execCommand('copy'); alert('已复制');}catch(e){ alert('复制失败'); } finally{ document.body.removeChild(ta); }
    });
  }else{
    const ta=document.createElement('textarea'); ta.value=txt; document.body.appendChild(ta); ta.select(); try{document.execCommand('copy'); alert('已复制');}catch(e){ alert('复制失败'); } finally{ document.body.removeChild(ta); }
  }
}
window.copyGateway = copyGateway;

async function load(){
  console.log("load function started");
  document.getElementById('gateway-base').textContent = gatewayBase();
  console.log("gatewayBase populated");
  try {
    const provs = await (await fetch('/ext/providers/configs')).json();
    console.log("provs fetched", provs);
    const keys = await (await fetch('/ext/providers')).json();
    console.log("keys fetched", keys);
    // 加载用户API并填充
    const u = await (await fetch('/ext/user_api')).json();
    console.log("user_api fetched", u);
    const apis = (u.user_apis||[]);
    document.getElementById('user-api').value = apis.join('\n');
    const sel = document.getElementById('test-api'); sel.innerHTML = '<option value="">选择一个用户API测试…</option>' + apis.map(a=>`<option value="${a}">${a.slice(0,6)}…${a.slice(-4)}</option>`).join('');
    const box = document.getElementById('providers'); box.innerHTML = '';
    const names = Array.from(new Set([...Object.keys(provs||{}), ...Object.keys(keys||{})]));
    names.forEach(name=>{
      const cfg = provs[name]||{}; const keyCount = keys[name]||0;
      const card = document.createElement('div'); card.className='p-2 border rounded mb-2';
      card.innerHTML = `
        <div class="flex items-center gap-2">
          <strong>${name}</strong>
          <span class="text-xs text-gray-500">keys: ${keyCount}</span>
          <label class="ml-auto text-xs">启用 <input type="checkbox" ${cfg.enabled?'checked':''} onchange="toggleProvider('${name}', this.checked)"></label>
        </div>
        <div class="mt-2 grid grid-cols-1 md:grid-cols-3 gap-2">
          <label>Base URL <input id="base-${name}" value="${cfg.base_url||''}" class="form-input w-full"/></label>
          <label>区域(逗号) <input id="regions-${name}" value="${(cfg.supported_regions||[]).join(',')}" class="form-input w-full"/></label>
          <label>API组(逗号) <input id="apis-${name}" value="${(cfg.api_groups||[]).join(',')}" class="form-input w-full"/></label>
        </div>
        <div class="mt-2 flex gap-2">
          <button onclick="saveCfg('${name}')" class="bg-blue-600 text-white px-2 py-1 rounded">保存</button>
          <a href="/ext/admin/providers/${name}" class="underline">密钥管理</a>
          <button onclick="testProvider('${name}')" class="bg-gray-700 text-white px-2 py-1 rounded">连通测试</button>
          <button onclick="importKeys('${name}')" class="bg-gray-500 text-white px-2 py-1 rounded">批量导入API</button>
          <button onclick="showModels('${name}')" class="bg-green-600 text-white px-2 py-1 rounded">显示支持模型</button>
          <button onclick="copyModels('${name}')" class="bg-green-700 text-white px-2 py-1 rounded">复制模型列表</button>
        </div>
        <div class="text-xs text-gray-500 mt-1">示例调用: curl -H 'Authorization: Bearer YOUR_KEY' ${gatewayBase()+"/chat/completions"}</div>
      `;
      box.appendChild(card);
    });
    // 渲染疑似失效keys
    const invalid = await (await fetch('/ext/providers/invalid_keys')).json();
    console.log("invalid keys fetched", invalid);
    const invBox = document.getElementById('invalid-keys'); invBox.innerHTML='';
    Object.keys(invalid||{}).forEach(name=>{
      const items = invalid[name]||{}; const ks = Object.keys(items).filter(k=>items[k]>=3);
      if(!ks.length) return;
      const sec = document.createElement('div'); sec.className='mb-2';
      sec.innerHTML = `<div class="font-semibold">${name}</div>` + ks.map(k=>{
        const cnt = items[k];
        return `<div class="flex items-center gap-2"><code>${k}</code><span>失败:${cnt}</span><button class="bg-red-600 text-white px-2 py-0.5 rounded" onclick="delInvalid('${name}','${k}')">确认删除</button><button class="bg-gray-300 px-2 py-0.5 rounded" onclick="resetInvalid('${name}','${k}')">忽略并重置</button></div>`;
      }).join('');
      invBox.appendChild(sec);
    });
  } catch (error) {
    console.error("Error in load function:", error);
  }
  console.log("load function finished");
}
window.load = load;

async function delInvalid(name, key){
  if(!confirm('确认删除该key?')) return;
  await fetch('/ext/providers/invalid_keys/delete', {method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({provider:name, key})});
  await load();
}
window.delInvalid = delInvalid;

async function copyModels(name){
  try{
    const res = await fetch(`/ext/providers/${name}/models`);
    const data = await res.json();
    const models = (data.result && data.result.data) ? (data.result.data.map(m=>m.id)) 
                  : (data.result && data.result.models) ? (data.result.models.map(m=>m.id||m.name||'')) 
                  : [];
    const txt = models.join('\n');
    if(!txt){ alert('未取到模型列表'); return; }
    if(navigator.clipboard && navigator.clipboard.writeText){
      await navigator.clipboard.writeText(txt);
      alert('已复制模型列表');
    }else{
      const ta=document.createElement('textarea'); ta.value=txt; document.body.appendChild(ta); ta.select();
      try{ document.execCommand('copy'); alert('已复制模型列表'); }catch(e){ alert('复制失败'); }
      finally{ document.body.removeChild(ta); }
    }
  }catch(e){ alert('复制失败: '+e); }
}
window.copyModels = copyModels;

async function resetInvalid(name, key){
  await fetch('/ext/providers/invalid_keys/reset', {method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({provider:name, key})});
  await load();
}
window.resetInvalid = resetInvalid;

async function importKeys(name){
  const raw = prompt('粘贴多个API Key，换行分隔：');
  if(!raw) return;
  const items = raw.split(/\n+/).map(x=>x.trim()).filter(Boolean);
  if(!items.length) return;
  await fetch(`/ext/providers/${name}/keys`, {method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({keys: items})});
  await load();
}
window.importKeys = importKeys;

async function showModels(name){
  try{
    const res = await fetch(`/ext/providers/${name}/models`);
    const data = await res.json();
    alert('支持模型:\n'+JSON.stringify(data.result, null, 2));
  }catch(e){ alert('获取模型失败: '+e); }
}
window.showModels = showModels;

async function toggleProvider(name, enabled){
  await fetch(`/ext/providers/${name}/config`, {method:'PUT', headers:{'Content-Type':'application/json'}, body: JSON.stringify({enabled})});
}
window.toggleProvider = toggleProvider;

async function saveCfg(name){
  const base_url = document.getElementById(`base-${name}`).value.trim();
  const supported_regions = (document.getElementById(`regions-${name}`).value||'').split(',').map(x=>x.trim()).filter(Boolean);
  const api_groups = (document.getElementById(`apis-${name}`).value||'').split(',').map(x=>x.trim()).filter(Boolean);
  await fetch(`/ext/providers/${name}/config`, {method:'PUT', headers:{'Content-Type':'application/json'}, body: JSON.stringify({base_url, supported_regions, api_groups})});
}
window.saveCfg = saveCfg;

async function testProvider(name){
  // 触发 e2e 连通测试：读取 warm 的一页 URL，调用后端 /ext/proxies/test 带上 providers=[name]
  const detail = await (await fetch('/ext/proxies/detail?warm_page=1&warm_page_size=20')).json();
  const urls = (detail.warm||[]).map(x=>x.url);
  if(!urls.length){ alert('暂无可用于测试的 Warm 代理'); return; }
  const res = await fetch('/ext/proxies/test', {method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({urls, providers:[name]})});
  const data = await res.json();
  alert('测试结果: '+JSON.stringify(data, null, 2));
}
window.testProvider = testProvider;

async function saveUserApi(){
  console.log("saveUserApi function started");
  const raw = document.getElementById('user-api').value.trim();
  const list = raw.split(/\n+/).map(x=>x.trim()).filter(Boolean);
  console.log("user_apis to save:", list);
  try {
    const response = await fetch('/ext/user_api', {method:'PUT', headers:{'Content-Type':'application/json'}, body: JSON.stringify({user_apis: list})});
    console.log("saveUserApi response:", response);
    if (!response.ok) {
      console.error("saveUserApi failed with status:", response.status);
      const errorText = await response.text();
      console.error("saveUserApi error text:", errorText);
      alert('保存失败');
      return;
    }
    alert('已保存');
    await load();
  } catch (error) {
    console.error("saveUserApi exception:", error);
    alert('保存失败');
  }
}
window.saveUserApi = saveUserApi;

async function testUserApi(){
  const api = document.getElementById('test-api').value.trim();
  if(!api){ alert('请选择用于测试的用户API'); return; }
  const prompt = document.getElementById('test-prompt').value || 'Hello';
  const payload = {
    model: 'gpt-3.5-turbo',
    api_key: api,
    messages: [{role:'user', content: prompt}],
    stream: false
  };
  try{
    const res = await fetch(gatewayBase()+'/chat/completions', {method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify(payload)});
    const data = await res.json();
    alert('测试结果:\n'+JSON.stringify(data, null, 2));
  }catch(e){ alert('测试失败: '+e); }
}
window.testUserApi = testUserApi;

window.addEventListener('load', load);
</script>
{% endblock %}