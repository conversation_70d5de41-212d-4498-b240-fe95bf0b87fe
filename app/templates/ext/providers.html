{% extends "base.html" %}
{% block content %}
<h2>多服务商管理</h2>
<div>
  <h3>服务商列表</h3>
  <table class="table">
    <thead><tr><th>服务商</th><th>已配置Key数量</th><th>启用</th><th>Base URL</th><th>区域</th><th>API组</th><th>操作</th></tr></thead>
    <tbody>
      {% for name, count in providers.items() %}
      {% set cfg = configs.get(name) %}
      <tr>
        <td>{{ name }}</td>
        <td>{{ count }}</td>
        <td><input type="checkbox" onchange="toggleProvider('{{ name }}', this.checked)" {% if cfg and cfg.enabled %}checked{% endif %}></td>
        <td><input type="text" value="{{ cfg.base_url if cfg else '' }}" onchange="updateField('{{ name }}','base_url', this.value)" style="width:18rem"></td>
        <td><input type="text" value="{{ (cfg.supported_regions|join(',')) if cfg and cfg.supported_regions else '' }}" onchange="updateCsv('{{ name }}','supported_regions', this.value)" style="width:10rem"></td>
        <td><input type="text" value="{{ (cfg.api_groups|join(',')) if cfg and cfg.api_groups else '' }}" onchange="updateCsv('{{ name }}','api_groups', this.value)" style="width:10rem"></td>
        <td><a href="/ext/admin/providers/{{ name }}">密钥</a></td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>

<script>
async function toggleProvider(name, enabled){
  await fetch(`/ext/providers/${name}/config`, {method:'PUT', headers:{'Content-Type':'application/json'}, body: JSON.stringify({enabled})});
}
async function updateField(name, field, value){
  const body = {}; body[field] = value; await fetch(`/ext/providers/${name}/config`, {method:'PUT', headers:{'Content-Type':'application/json'}, body: JSON.stringify(body)});
}
async function updateCsv(name, field, value){
  const items = (value||'').split(',').map(x=>x.trim()).filter(Boolean);
  const body = {}; body[field] = items; await fetch(`/ext/providers/${name}/config`, {method:'PUT', headers:{'Content-Type':'application/json'}, body: JSON.stringify(body)});
}
</script>
{% endblock %}
