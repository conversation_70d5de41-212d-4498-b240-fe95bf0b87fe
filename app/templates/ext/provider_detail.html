{% extends "base.html" %}
{% block content %}
<h2>服务商：{{ provider }}</h2>
<form id="keys-form">
  <label>API Keys（每行一个）</label>
  <textarea id="keys" rows="8" style="width:100%">{% for k in keys %}{{ k }}
{% endfor %}</textarea>
  <button type="button" onclick="saveKeys()">保存</button>
</form>
<hr />
<h3>主池绑定（{{ provider }}）</h3>
<div class="glass-card p-3 rounded">
  <div class="mb-2">
    <label>API 组</label>
    <select id="api-group" class="form-select">
      <option>chat</option>
      <option>embeddings</option>
      <option>images</option>
    </select>
    <button type="button" onclick="loadMain()" class="bg-blue-500 px-2 py-1 rounded">刷新</button>
  </div>
  <div>
    <div class="mb-2">
      <textarea id="bind-urls" rows="4" placeholder="每行一个代理URL" class="form-input" style="width:100%"></textarea>
    </div>
    <button type="button" onclick="bindToMain()" class="bg-blue-500 px-3 py-1 rounded">绑定到主池</button>
    <button type="button" onclick="unbindAll()" class="bg-red-500 px-3 py-1 rounded">清空该组绑定</button>
  </div>
  <pre id="main-view" class="p-2 bg-gray-50 rounded text-xs mt-2"></pre>
</div>
<script>
async function saveKeys(){
  const raw = document.getElementById('keys').value.trim();
  const items = raw.split(/\n+/).map(x=>x.trim()).filter(Boolean);
  const res = await fetch(`/ext/providers/{{ provider }}/keys`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ keys: items })
  });
  if(res.ok){ alert('保存成功'); } else { alert('保存失败'); }
}
async function loadMain(){
  const api_group = document.getElementById('api-group').value;
  document.getElementById('main-view').textContent = JSON.stringify([], null, 2);
}
async function bindToMain(){
  alert('代理池功能已移除');
}
async function unbindAll(){
  alert('代理池功能已移除');
}
window.addEventListener('load', loadMain);
</script>
{% endblock %}
