from __future__ import annotations
import sys
import subprocess
import logging
from pathlib import Path
from typing import Optional


class VirtualEnvironmentActivator:
    def __init__(self, project_root: Path) -> None:
        self.project_root = project_root
        self.venv_path = project_root / '.venv'
        self.requirements_path = project_root / 'requirements.txt'
        self.activation_status = {"activated": False, "missing_packages": [], "error": None}

        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

    def get_activation_status(self) -> dict:
        return self.activation_status

    def activate_and_validate(self) -> None:
        self.logger.info("Activating and validating virtual environment...")
        if not self.venv_path.exists():
            self.activation_status["error"] = "Virtual environment not found."
            self.logger.error(f"Virtual environment not found at: {self.venv_path}")
            return

        # Activate by adding site-packages to sys.path
        site_packages = self.venv_path / 'lib' / f'python{sys.version_info.major}.{sys.version_info.minor}' / 'site-packages'
        if str(site_packages) not in sys.path:
            sys.path.insert(0, str(site_packages))
        
        self.activation_status["activated"] = True
        self._check_dependencies()

    def _check_dependencies(self):
        self.logger.info("Checking dependencies...")
        try:
            import pkg_resources
        except ImportError:
            self.activation_status["error"] = "`pkg_resources` not found. Please install `setuptools`."
            self.logger.error("`pkg_resources` not found. Please install `setuptools`.")
            return

        if not self.requirements_path.exists():
            self.activation_status["error"] = "`requirements.txt` not found."
            self.logger.error(f"`requirements.txt` not found at: {self.requirements_path}")
            return

        with open(self.requirements_path, 'r') as f:
            requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]
        
        try:
            missing = {req.project_name for req in pkg_resources.parse_requirements(requirements)} - {dist.project_name for dist in pkg_resources.working_set}
            self.activation_status["missing_packages"] = list(missing)
            if missing:
                self.logger.warning(f"Missing packages: {missing}")
        except Exception as e:
            self.activation_status["error"] = f"Error checking dependencies: {e}"
            self.logger.error(f"Error checking dependencies: {e}")

    def install_missing_packages(self) -> None:
        missing = self.activation_status.get("missing_packages", [])
        if not missing:
            self.logger.info("No missing packages to install.")
            return

        self.logger.info(f"Installing missing packages: {missing}")
        pip_executable = self.venv_path / 'bin' / 'pip'
        for package in missing:
            try:
                subprocess.check_call([str(pip_executable), 'install', package])
                self.logger.info(f"Successfully installed {package}")
            except subprocess.CalledProcessError as e:
                self.activation_status["error"] = f"Failed to install {package}: {e}"
                self.logger.error(f"Failed to install {package}: {e}")
                return
        
        # Clear missing packages after installation
        self.activation_status["missing_packages"] = []


_activator: Optional[VirtualEnvironmentActivator] = None


def get_venv_activator() -> VirtualEnvironmentActivator:
    global _activator
    if _activator is None:
        project_root = Path(__file__).parent.parent.parent
        _activator = VirtualEnvironmentActivator(project_root)
    return _activator
