import uuid
import contextvars
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import Response
from starlette.types import ASGI<PERSON>pp
from typing import Optional

# Context variable to store the request ID
request_id_var = contextvars.ContextVar("request_id", default=None)

class RequestIDMiddleware(BaseHTTPMiddleware):
    def __init__(self, app: ASGIApp):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next):
        request_id = str(uuid.uuid4())
        # Store the request ID in the context variable
        token = request_id_var.set(request_id)
        
        # Add request ID to response headers
        request.state.request_id = request_id # Store in request state for easy access in handlers
        response = await call_next(request)
        response.headers["X-Request-ID"] = request_id
        
        # Reset the context variable
        request_id_var.reset(token)
        return response

# Helper function to get the current request ID
def get_request_id() -> Optional[str]:
    return request_id_var.get()
