#!/usr/bin/env python3
"""
测试完善的代理分类逻辑
验证延迟和匿名性的严格分类规则
"""

import asyncio
from app.database.connection import database
from extensions.proxy.manager import get_proxy_manager
from extensions.proxy.ip_pool import ProxyInfo

async def test_classification_rules():
    """测试分类规则"""
    await database.connect()
    
    try:
        mgr = get_proxy_manager()
        
        print("=== 测试完善的代理分类逻辑 ===")
        
        # 清空现有池以便测试
        mgr.pools._warm.clear()
        mgr.pools._fail.clear()
        for partition in mgr.pools._main.values():
            partition.main_by_api.clear()
        
        # 测试用例
        test_cases = [
            # (url, latency_ms, anonymity, expected_pool, description)
            ("http://fast-anon.com:8080", 150, "anonymous", "warm", "快速匿名代理 → 热池（主池候选）"),
            ("http://fast-elite.com:8080", 100, "elite", "warm", "极快精英代理 → 热池（主池候选）"),
            ("http://fast-transparent.com:8080", 120, "transparent", "fail", "快速透明代理 → 失败池"),
            ("http://medium-anon.com:8080", 500, "anonymous", "warm", "中等匿名代理 → 热池"),
            ("http://slow-anon.com:8080", 1500, "anonymous", "fail", "慢速匿名代理 → 失败池"),
            ("http://unknown-anon.com:8080", 150, None, "warm", "未知匿名性但快速 → 热池"),
            ("http://unknown-slow.com:8080", 1500, None, "fail", "未知匿名性且慢速 → 失败池"),
            ("http://no-latency.com:8080", None, "anonymous", "fail", "无延迟数据 → 失败池"),
        ]
        
        print(f"\n测试 {len(test_cases)} 个分类用例...")
        
        for i, (url, latency_ms, anonymity, expected_pool, description) in enumerate(test_cases, 1):
            print(f"\n{i}. {description}")
            print(f"   URL: {url}")
            print(f"   延迟: {latency_ms}ms, 匿名性: {anonymity}")
            
            # 创建代理信息
            proxy = ProxyInfo(proxy_url=url, anonymity=anonymity)
            
            # 先添加到热池（模拟新代理）
            mgr.pools._warm[url] = proxy
            
            # 进行分类
            await mgr.pools.classify(proxy, latency_ms)
            
            # 检查结果
            actual_pool = None
            if url in mgr.pools._warm:
                actual_pool = "warm"
            elif url in mgr.pools._fail:
                actual_pool = "fail"
            
            # 检查是否为主池候选
            is_main_candidate = getattr(proxy, 'is_main_candidate', False)
            
            if actual_pool == expected_pool:
                print(f"   ✅ 正确分类到 {actual_pool}池")
                if is_main_candidate and latency_ms and latency_ms <= 200 and anonymity in ['anonymous', 'elite', 'high_anonymous']:
                    print(f"   ✅ 正确标记为主池候选")
                elif not is_main_candidate and (not latency_ms or latency_ms > 200 or anonymity not in ['anonymous', 'elite', 'high_anonymous']):
                    print(f"   ✅ 正确未标记为主池候选")
                else:
                    print(f"   ⚠️  主池候选标记可能有误: {is_main_candidate}")
            else:
                print(f"   ❌ 分类错误: 期望{expected_pool}池，实际{actual_pool}池")
        
        # 测试主池绑定
        print(f"\n=== 测试主池绑定逻辑 ===")
        
        # 准备主池候选代理
        main_candidates = [
            mgr.pools._warm.get("http://fast-anon.com:8080"),
            mgr.pools._warm.get("http://fast-elite.com:8080"),
        ]
        main_candidates = [p for p in main_candidates if p]
        
        if main_candidates:
            print(f"找到 {len(main_candidates)} 个主池候选代理")
            
            # 测试绑定到主池
            await mgr.pools.bind_main_for_provider_api("test_provider", "test_api", main_candidates)
            
            # 检查绑定结果
            main_snapshot, _, _ = mgr.pools.snapshot()
            bound_urls = main_snapshot.get("test_provider", {}).get("test_api", [])
            
            print(f"成功绑定 {len(bound_urls)} 个代理到主池")
            for url in bound_urls:
                print(f"   - {url}")
        
        # 测试代理选择
        print(f"\n=== 测试代理选择逻辑 ===")
        
        # 测试从主池选择
        selected = await mgr.pools.choose_for("test_provider", "test_api")
        if selected:
            print(f"✅ 从主池选择代理: {selected}")
        else:
            print(f"❌ 无法从主池选择代理")
        
        # 测试地区排除
        print(f"\n=== 测试地区排除逻辑 ===")
        
        # 添加有地区信息的代理
        us_proxy = ProxyInfo(proxy_url="http://us-proxy.com:8080", region="US", anonymity="anonymous")
        cn_proxy = ProxyInfo(proxy_url="http://cn-proxy.com:8080", region="CN", anonymity="anonymous")
        
        mgr.pools._warm["http://us-proxy.com:8080"] = us_proxy
        mgr.pools._warm["http://cn-proxy.com:8080"] = cn_proxy
        
        # 分类这些代理
        await mgr.pools.classify(us_proxy, 150)
        await mgr.pools.classify(cn_proxy, 150)
        
        # 绑定到主池
        await mgr.pools.bind_main_for_provider_api("test_provider", "test_api", [us_proxy, cn_proxy])
        
        # 测试排除CN地区
        selected_exclude_cn = await mgr.pools.choose_for("test_provider", "test_api", exclude_countries=["CN"])
        print(f"排除CN后选择的代理: {selected_exclude_cn}")
        
        if selected_exclude_cn and "cn-proxy" not in selected_exclude_cn:
            print(f"✅ 地区排除功能正常")
        else:
            print(f"❌ 地区排除功能异常")
        
        print(f"\n=== 测试完成 ===")
        
    finally:
        await database.disconnect()

async def test_recovery_mechanism():
    """测试代理恢复机制"""
    await database.connect()
    
    try:
        print(f"\n=== 测试代理恢复机制 ===")
        
        mgr = get_proxy_manager()
        
        # 创建一个失败的代理
        failed_proxy = ProxyInfo(proxy_url="http://recovery-test.com:8080", anonymity="anonymous")
        failed_proxy.failure_count = 2
        mgr.pools._fail["http://recovery-test.com:8080"] = failed_proxy
        
        print(f"创建失败代理，失败次数: {failed_proxy.failure_count}")
        
        # 测试恢复：延迟改善
        await mgr.pools.classify(failed_proxy, 150)  # 优秀延迟
        
        # 检查是否恢复到热池
        if "http://recovery-test.com:8080" in mgr.pools._warm:
            print(f"✅ 代理成功恢复到热池")
            print(f"   失败次数减少到: {failed_proxy.failure_count}")
            print(f"   是否为主池候选: {getattr(failed_proxy, 'is_main_candidate', False)}")
        else:
            print(f"❌ 代理未能恢复到热池")
        
    finally:
        await database.disconnect()

async def main():
    """主测试函数"""
    print("开始测试完善的代理分类逻辑...")
    
    await test_classification_rules()
    await test_recovery_mechanism()
    
    print("\n所有测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
