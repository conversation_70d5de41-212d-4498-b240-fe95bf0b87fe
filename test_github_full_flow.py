#!/usr/bin/env python3
"""
测试GitHub完整流程
"""

import asyncio
import json
import os
from pathlib import Path

async def test_github_full_flow():
    """测试GitHub完整流程"""
    print("=== GitHub完整流程测试 ===\n")
    
    # 设置环境
    os.environ["GITHUB_TOKEN"] = "*********************************************************************************************"
    
    # 1. 重置GitHub爬取状态
    print("🔄 步骤1: 重置GitHub爬取状态")
    state_file = Path("extensions/proxy/proxies_state.json")
    if state_file.exists():
        try:
            state_data = json.loads(state_file.read_text(encoding="utf-8"))
            if "github_last_crawled" in state_data:
                del state_data["github_last_crawled"]
                state_file.write_text(json.dumps(state_data, indent=2), encoding="utf-8")
                print("✅ 已重置GitHub爬取时间戳")
        except:
            pass
    
    # 2. 运行GitHub Scout（发现仓库）
    print(f"\n🔍 步骤2: GitHub Scout（发现仓库）")
    try:
        from extensions.proxy.crawlers.github_crawler import crawl_github_sources
        
        print("开始GitHub Scout...")
        await crawl_github_sources()
        print("✅ GitHub Scout完成")
        
    except Exception as e:
        print(f"❌ GitHub Scout失败: {e}")
        return
    
    # 3. 检查发现的仓库数量
    print(f"\n📊 步骤3: 检查发现的仓库")
    try:
        from extensions.proxy.config import get_proxy_sources_config
        
        config_store = get_proxy_sources_config()
        await config_store._load()  # 重新加载配置
        
        sources_config = config_store.get()
        repos_to_analyze = sources_config.github_repositories_to_scan
        
        print(f"待分析仓库数量: {len(repos_to_analyze)}")
        
        if repos_to_analyze:
            print("待分析仓库（前20个）:")
            for i, repo in enumerate(repos_to_analyze[:20]):
                print(f"  {i+1}. {repo}")
        else:
            print("❌ 没有发现待分析仓库")
            return
        
    except Exception as e:
        print(f"❌ 检查仓库失败: {e}")
        return
    
    # 4. 运行GitHub Analyst（分析仓库）
    print(f"\n🔬 步骤4: GitHub Analyst（分析仓库）")
    try:
        from extensions.proxy.crawlers.site_crawler import analyze_new_github_repos
        
        before_count = len(repos_to_analyze)
        print(f"开始分析 {before_count} 个仓库...")
        
        await analyze_new_github_repos()
        
        # 重新加载配置检查结果
        await config_store._load()
        sources_config = config_store.get()
        after_repos = sources_config.github_repositories_to_scan
        after_count = len(after_repos)
        
        analyzed_count = before_count - after_count
        print(f"✅ GitHub Analyst完成")
        print(f"   分析前: {before_count} 个仓库")
        print(f"   分析后: {after_count} 个仓库")
        print(f"   实际分析: {analyzed_count} 个仓库")
        
        if after_count > 0:
            print(f"   剩余待分析: {after_count} 个仓库")
        
    except Exception as e:
        print(f"❌ GitHub Analyst失败: {e}")
        return
    
    # 5. 检查新发现的代理源
    print(f"\n🎯 步骤5: 检查新发现的代理源")
    try:
        # 重新加载配置
        await config_store._load()
        sources_config = config_store.get()
        
        txt_count = len(sources_config.txt_sources)
        api_count = len(sources_config.api_sources)
        site_count = len(sources_config.site_sources)
        
        print(f"代理源统计:")
        print(f"  TXT源: {txt_count}")
        print(f"  API源: {api_count}")
        print(f"  网站源: {site_count}")
        print(f"  总计: {txt_count + api_count + site_count}")
        
        if sources_config.txt_sources:
            print(f"\n新TXT源（最新10个）:")
            for i, source in enumerate(sources_config.txt_sources[-10:]):
                print(f"  {i+1}. {source}")
        
        if sources_config.api_sources:
            print(f"\n新API源（最新5个）:")
            for i, source in enumerate(sources_config.api_sources[-5:]):
                print(f"  {i+1}. {source}")
        
    except Exception as e:
        print(f"❌ 检查代理源失败: {e}")
    
    # 6. 检查缓存状态
    print(f"\n💾 步骤6: 检查缓存状态")
    try:
        cache_file = Path("extensions/proxy/repo_analysis_cache.json")
        if cache_file.exists():
            cache_data = json.loads(cache_file.read_text(encoding="utf-8"))
            
            total_cached = len(cache_data)
            valid_count = sum(1 for data in cache_data.values() if data.get("status") == "valid")
            invalid_count = sum(1 for data in cache_data.values() if data.get("status") == "invalid")
            
            print(f"缓存统计:")
            print(f"  总缓存仓库: {total_cached}")
            print(f"  有效仓库: {valid_count}")
            print(f"  无效仓库: {invalid_count}")
            
            # 显示最新分析的仓库
            sorted_cache = sorted(cache_data.items(), 
                                key=lambda x: x[1].get("last_analyzed", 0), 
                                reverse=True)
            
            print(f"\n最新分析的仓库（前10个）:")
            for i, (repo_name, data) in enumerate(sorted_cache[:10]):
                status = data.get("status", "unknown")
                print(f"  {i+1}. {repo_name}: {status}")
        else:
            print("❌ 缓存文件不存在")
    
    except Exception as e:
        print(f"❌ 检查缓存失败: {e}")
    
    # 7. 总结
    print(f"\n" + "="*60)
    print("🎉 GitHub完整流程测试完成！")
    print("="*60)
    
    print(f"\n📋 流程总结:")
    print(f"✅ GitHub Scout: 发现仓库并添加到待分析列表")
    print(f"✅ GitHub Analyst: 分析仓库并提取代理源")
    print(f"✅ 缓存机制: 记录分析结果，避免重复分析")
    print(f"✅ 源发现: 自动发现新的TXT/API/网站代理源")
    
    if analyzed_count > 0:
        print(f"\n🎯 本次成果:")
        print(f"  分析仓库: {analyzed_count} 个")
        print(f"  发现TXT源: {txt_count} 个")
        print(f"  发现API源: {api_count} 个")
        print(f"  发现网站源: {site_count} 个")
    else:
        print(f"\n⚠️  注意: 本次没有分析新仓库（可能都已缓存）")

async def main():
    """主函数"""
    await test_github_full_flow()

if __name__ == "__main__":
    asyncio.run(main())
