{"summary": {"total_checked": 598, "candidate_main": 0, "candidate_fail": 0, "keep_fail_non_anon": 0, "unknown": 598, "latency_threshold_ms": 800, "db_path": "/home/<USER>/应用/轮询/data/default_db", "table": "t_proxies", "write_back": true, "db_updates": {"to_main": 0, "to_fail": 0}}, "candidate_main": [], "candidate_fail": [], "keep_fail_non_anon": [], "unknown": [{"proxy": "http://**************:11001", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:999", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:35471", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:30504", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:8889", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:16000", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:8083", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:3128", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:1088", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:3128", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:5031", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:3366", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:45250", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:52210", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:8089", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:50001", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:1981", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8561", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:6370", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:2020", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:5430", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:23500", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:53281", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:31008", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:5555", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:4153", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:4153", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:8004", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:59166", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:4153", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:53471", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:27676", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:999", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:33080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:9999", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:49205", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:30906", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:53281", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:3128", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:58148", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:999", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:8118", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:999", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:39593", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:53281", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:5715", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:30102", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:47377", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:4153", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:36476", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:9991", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:9813", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:24585", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:33333", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:33333", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:10800", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:10800", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:443", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:8181", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:61303", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:4153", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:32999", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:4153", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:10801", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:31433", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:12000", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:3128", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:41162", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:16379", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:35010", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:55443", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:55443", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:17", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:2082", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:999", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:54321", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:6456", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:50001", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:44887", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:5060", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:41047", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:16379", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:2080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:39593", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8008", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:16000", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:46449", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8118", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:4153", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:27832", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:999", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:32241", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:16088", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:40308", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:9797", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:40668", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:39593", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:3128", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:39593", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:4153", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8989", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:9080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:58309", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:45155", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:60063", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:50001", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:9898", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:49205", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:3128", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:19132", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:16000", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:49205", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:4682", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:4153", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:7302", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:3129", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:83", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:35578", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:39593", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:43718", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:999", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:23500", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:6584", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:4002", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:7777", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:6568", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://********:18081", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8975", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8443", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:10800", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:1337", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8118", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8383", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:6699", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:4153", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:999", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:10600", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:3128", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:53854", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:47239", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:7302", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:8888", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:4153", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:4153", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:50001", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:3927", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:3128", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:3629", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:20", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:41890", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:4153", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:3128", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:999", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:31948", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:59166", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:4153", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:993", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:20143", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:25275", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:37", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:4153", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:83", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:999", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8192", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:3306", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:1452", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:999", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:61500", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:3129", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:18351", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:8090", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:4153", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8089", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:50001", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:3629", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:4153", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:51056", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:16379", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:53128", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:31019", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://**************:19132", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:6969", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***************:3129", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:50540", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://*************:14888", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://***********:16000", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "http://************:6422", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:27676", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:37430", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:32999", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:999", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:31908", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:16000", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***********:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***********:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:1452", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:8888", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***********:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:3128", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:5715", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:3128", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:999", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:83", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:53281", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:8008", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:7777", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:999", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:6584", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:3927", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:60063", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:4153", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:47239", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:16000", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:5031", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:3128", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:8443", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:999", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:5060", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:61500", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:13024", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:2005", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:18080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:3128", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:34409", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:6370", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:8004", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:43718", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**********:1081", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:4682", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***********:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:5031", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:58839", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:31433", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:4153", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:6568", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:44887", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***********:16000", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:4153", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:16379", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:16088", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:8080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:83", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***********:20", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:16000", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:3128", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:2020", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:4002", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:9090", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:18351", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:8200", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:63518", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:16000", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:30906", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:45250", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:20269", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:1020", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:8181", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:46449", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:17", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:53018", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:42495", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:45155", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:999", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:14070", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:3129", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:999", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:53281", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:8561", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:8975", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:55443", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:6969", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***********:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:14888", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:20143", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:4153", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:6422", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:61507", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:3129", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:12000", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:5031", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:1080", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:6456", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:1981", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**********:5430", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***********:6969", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:1088", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***********:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***********:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:37", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:83", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:3128", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:15864", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***********:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:3128", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://***************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:33333", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://*************:33333", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:8085", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:3129", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:4145", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:5678", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://************:6443", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}, {"proxy": "socks5://**************:80", "latency_ms": null, "anonymity": null, "direct_ip": "**************", "proxy_ip": null, "error": "ip_via_proxy_error: AsyncClient.__init__() got an unexpected keyword argument 'proxies'"}]}