"""Add ProxySource table

Revision ID: 924a4c618930
Revises: ee89a9511985
Create Date: 2025-08-19 22:06:53.935649

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '924a4c618930'
down_revision: Union[str, Sequence[str], None] = 'ee89a9511985'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('t_proxy_sources',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('source_type', sa.String(length=50), nullable=False, comment='源类型 (txt, site, api, github)'),
    sa.Column('url', sa.String(length=500), nullable=False, comment='源URL'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('url')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('t_proxy_sources')
    # ### end Alembic commands ###
