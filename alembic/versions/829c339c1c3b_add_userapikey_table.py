"""Add UserApiKey table

Revision ID: 829c339c1c3b
Revises: 666b9ac2c008
Create Date: 2025-08-19 22:40:14.946639

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '829c339c1c3b'
down_revision: Union[str, Sequence[str], None] = '666b9ac2c008'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('t_user_api_keys',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('api_key', sa.String(length=500), nullable=False, comment='用户API密钥'),
    sa.Column('status', sa.String(length=50), nullable=True, comment='密钥状态'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('api_key')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('t_user_api_keys')
    # ### end Alembic commands ###
