"""Add ApiKey table

Revision ID: fb1567de0249
Revises: a0faf86d6baf
Create Date: 2025-08-19 22:29:52.954080

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'fb1567de0249'
down_revision: Union[str, Sequence[str], None] = 'a0faf86d6baf'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('t_api_keys',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('provider_name', sa.String(length=100), nullable=False, comment='服务商名称'),
    sa.Column('encrypted_key', sa.String(length=500), nullable=False, comment='加密后的API密钥'),
    sa.Column('status', sa.String(length=50), nullable=True, comment='密钥状态'),
    sa.Column('last_used', sa.DateTime(), nullable=True, comment='最后使用时间'),
    sa.Column('usage_count', sa.Integer(), nullable=True, comment='使用次数'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('t_api_keys')
    # ### end Alembic commands ###
