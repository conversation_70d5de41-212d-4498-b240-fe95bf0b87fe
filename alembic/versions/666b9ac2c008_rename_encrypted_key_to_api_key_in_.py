"""Rename encrypted_key to api_key in ApiKey table

Revision ID: 666b9ac2c008
Revises: fb1567de0249
Create Date: 2025-08-19 22:36:45.507986

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '666b9ac2c008'
down_revision: Union[str, Sequence[str], None] = 'fb1567de0249'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('t_api_keys', sa.Column('api_key', sa.String(length=500), nullable=False, comment='API密钥'))
    op.drop_column('t_api_keys', 'encrypted_key')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('t_api_keys', sa.Column('encrypted_key', sa.VARCHAR(length=500), nullable=False))
    op.drop_column('t_api_keys', 'api_key')
    # ### end Alembic commands ###
