"""Add ProviderConfig table

Revision ID: a0faf86d6baf
Revises: 924a4c618930
Create Date: 2025-08-19 22:24:01.387417

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a0faf86d6baf'
down_revision: Union[str, Sequence[str], None] = '924a4c618930'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('t_provider_configs',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('provider_name', sa.String(length=100), nullable=False, comment='服务商名称'),
    sa.Column('enabled', sa.<PERSON>(), nullable=True, comment='是否启用'),
    sa.Column('base_url', sa.String(length=500), nullable=False, comment='基础URL'),
    sa.Column('supported_regions', sa.JSON(), nullable=True, comment='支持的区域'),
    sa.Column('exclude_countries', sa.JSON(), nullable=True, comment='排除的国家'),
    sa.Column('api_groups', sa.JSON(), nullable=True, comment='API分组'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('provider_name')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('t_provider_configs')
    # ### end Alembic commands ###
