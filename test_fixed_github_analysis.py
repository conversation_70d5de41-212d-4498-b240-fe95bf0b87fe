#!/usr/bin/env python3
"""
测试修复后的GitHub仓库分析
"""

import asyncio
import json
import os
from pathlib import Path

async def test_fixed_analysis():
    """测试修复后的分析"""
    print("=== 测试修复后的GitHub仓库分析 ===\n")
    
    # 设置环境
    os.environ["GITHUB_TOKEN"] = "*********************************************************************************************"
    
    # 1. 清理配置，重新开始
    print("🔄 步骤1: 清理配置")
    try:
        from extensions.proxy.config import get_proxy_sources_config
        
        config_store = get_proxy_sources_config()
        
        # 清空待分析仓库列表
        sources_config = config_store.get()
        sources_config.github_repositories_to_scan = []
        config_store.save(sources_config)
        
        print("✅ 已清空待分析仓库列表")
        
    except Exception as e:
        print(f"❌ 清理配置失败: {e}")
        return
    
    # 2. 获取新仓库
    print(f"\n🔍 步骤2: 获取新仓库")
    try:
        from extensions.proxy.crawlers.github_crawler import search_proxy_repositories, _load_analyzed_repos
        
        analyzed_repos = _load_analyzed_repos()
        print(f"已分析仓库: {len(analyzed_repos)}")
        
        repo_urls = await search_proxy_repositories(analyzed_repos, max_pages=1)
        print(f"发现新仓库: {len(repo_urls)}")
        
        if not repo_urls:
            print("❌ 没有发现新仓库")
            return
        
        # 只取前30个进行测试
        test_repos = repo_urls[:30]
        print(f"测试仓库: {len(test_repos)}")
        
    except Exception as e:
        print(f"❌ 获取新仓库失败: {e}")
        return
    
    # 3. 添加到配置
    print(f"\n📦 步骤3: 添加到配置")
    try:
        await config_store.add_github_repositories(test_repos)
        
        sources_config = config_store.get()
        repos_to_analyze = sources_config.github_repositories_to_scan
        print(f"待分析仓库: {len(repos_to_analyze)}")
        
        if repos_to_analyze:
            print("待分析仓库（前10个）:")
            for i, repo in enumerate(repos_to_analyze[:10]):
                print(f"  {i+1}. {repo}")
        
    except Exception as e:
        print(f"❌ 添加到配置失败: {e}")
        return
    
    # 4. 运行分析（修复后的版本）
    print(f"\n🔬 步骤4: 运行分析（修复后）")
    try:
        from extensions.proxy.crawlers.site_crawler import analyze_new_github_repos
        
        before_count = len(repos_to_analyze)
        print(f"分析前待分析仓库: {before_count}")
        
        # 运行分析
        await analyze_new_github_repos()
        
        # 检查分析后状态
        sources_config = config_store.get()
        after_repos = sources_config.github_repositories_to_scan
        after_count = len(after_repos)
        
        print(f"分析后待分析仓库: {after_count}")
        print(f"实际分析了: {before_count - after_count} 个仓库")
        
        if after_count > 0:
            print("剩余待分析仓库:")
            for i, repo in enumerate(after_repos[:5]):
                print(f"  {i+1}. {repo}")
        
    except Exception as e:
        print(f"❌ 运行分析失败: {e}")
        return
    
    # 5. 检查新发现的源
    print(f"\n🎯 步骤5: 检查新发现的源")
    try:
        sources_config = config_store.get()
        
        print(f"TXT源数量: {len(sources_config.txt_sources)}")
        print(f"API源数量: {len(sources_config.api_sources)}")
        print(f"网站源数量: {len(sources_config.site_sources)}")
        
        if sources_config.txt_sources:
            print("新TXT源示例:")
            for i, source in enumerate(sources_config.txt_sources[-10:]):  # 显示最新的10个
                print(f"  {i+1}. {source}")
        
    except Exception as e:
        print(f"❌ 检查新源失败: {e}")
    
    # 6. 检查缓存更新
    print(f"\n💾 步骤6: 检查缓存更新")
    try:
        cache_file = Path("extensions/proxy/repo_analysis_cache.json")
        if cache_file.exists():
            cache_data = json.loads(cache_file.read_text(encoding="utf-8"))
            print(f"缓存仓库总数: {len(cache_data)}")
            
            valid_count = sum(1 for data in cache_data.values() if data.get("status") == "valid")
            invalid_count = sum(1 for data in cache_data.values() if data.get("status") == "invalid")
            
            print(f"  - 有效仓库: {valid_count}")
            print(f"  - 无效仓库: {invalid_count}")
            
            # 显示最新分析的仓库
            sorted_cache = sorted(cache_data.items(), 
                                key=lambda x: x[1].get("last_analyzed", 0), 
                                reverse=True)
            
            print("最新分析的仓库:")
            for i, (repo_name, data) in enumerate(sorted_cache[:5]):
                print(f"  {i+1}. {repo_name}: {data['status']}")
        
    except Exception as e:
        print(f"❌ 检查缓存失败: {e}")

async def main():
    """主函数"""
    await test_fixed_analysis()

if __name__ == "__main__":
    asyncio.run(main())
